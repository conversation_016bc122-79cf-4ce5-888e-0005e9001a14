import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {UserRoleAuthorization} from '../models';
import {UserProfileRepository, UserRoleAuthorizationRepository} from '../repositories';

export class UserRoleAuthorizationController {
  constructor(
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @repository(UserProfileRepository) public userProfileRepository: UserProfileRepository,
  ) { }

  @post('/user-role-authorizations')
  @response(200, {
    description: 'UserRoleAuthorization model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserRoleAuthorization)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserRoleAuthorization, {
            title: 'NewUserRoleAuthorization',
            exclude: ['id'],
          }),
        },
      },
    })
    userRoleAuthorization: Omit<UserRoleAuthorization, 'id'>,
  ): Promise<UserRoleAuthorization> {
    return this.userRoleAuthorizationRepository.create(userRoleAuthorization);
  }

  @get('/user-role-authorizations/count')
  @response(200, {
    description: 'UserRoleAuthorization model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(UserRoleAuthorization) where?: Where<UserRoleAuthorization>,
  ): Promise<Count> {
    return this.userRoleAuthorizationRepository.count(where);
  }

  @get('/user-role-authorizations')
  @response(200, {
    description: 'Array of UserRoleAuthorization model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserRoleAuthorization, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserRoleAuthorization) filter?: Filter<UserRoleAuthorization>,
  ): Promise<UserRoleAuthorization[]> {
    return this.userRoleAuthorizationRepository.find(filter);
  }

  @patch('/user-role-authorizations')
  @response(200, {
    description: 'UserRoleAuthorization PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserRoleAuthorization, {partial: true}),
        },
      },
    })
    userRoleAuthorization: UserRoleAuthorization,
    @param.where(UserRoleAuthorization) where?: Where<UserRoleAuthorization>,
  ): Promise<Count> {
    return this.userRoleAuthorizationRepository.updateAll(userRoleAuthorization, where);
  }

  @get('/user-role-authorizations/{id}')
  @response(200, {
    description: 'UserRoleAuthorization model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserRoleAuthorization, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(UserRoleAuthorization, {exclude: 'where'}) filter?: FilterExcludingWhere<UserRoleAuthorization>
  ): Promise<UserRoleAuthorization> {
    return this.userRoleAuthorizationRepository.findById(id, filter);
  }

  @patch('/user-role-authorizations/{id}')
  @response(204, {
    description: 'UserRoleAuthorization PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserRoleAuthorization, {partial: true}),
        },
      },
    })
    userRoleAuthorization: UserRoleAuthorization,
  ): Promise<void> {
    await this.userRoleAuthorizationRepository.updateById(id, userRoleAuthorization);
  }

  @put('/user-role-authorizations/{id}')
  @response(204, {
    description: 'UserRoleAuthorization PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() userRoleAuthorization: UserRoleAuthorization,
  ): Promise<void> {
    await this.userRoleAuthorizationRepository.replaceById(id, userRoleAuthorization);
  }

  @del('/user-role-authorizations/{id}')
  @response(204, {
    description: 'UserRoleAuthorization DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.userRoleAuthorizationRepository.deleteById(id);
  }
  @post('/individual-user-location-roles')
  @response(200, {
    description: 'IndividualUserLocationRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserRoleAuthorization)}},
  })
  async createIndividual(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {
                type: 'number',
              },
              user_id: {
                type: 'number',
              },
              roles: {
                type: 'array',
                items: {
                  type: 'number',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  tier1_id: {type: 'number', nullable: true},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
              },
              created_on: {type: 'string', nullable: true},
              created_by: {type: 'number', nullable: true},
              modified_on: {type: 'string', nullable: true},
              modified_by: {type: 'number', nullable: true},
            },
            required: ['userProfileId', 'locations', 'roles', 'user_id'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<any> {
    const {user_id, roles, locations, created_on, created_by, modified_on, modified_by, userProfileId} = requestBody;
    const savedUserLocationRoles: UserRoleAuthorization[] = [];

    const existingUserLocationRole = await this.userRoleAuthorizationRepository.findOne({
      where: {
        and: [
          {tier1_id: locations.tier1_id},
          {tier2_id: locations.tier2_id},
          {tier3_id: locations.tier3_id},
          {userProfileId: userProfileId},
          {user_id: user_id},
        ],
      },
    });

    if (existingUserLocationRole) {
      existingUserLocationRole.roles = roles;
      existingUserLocationRole.modified_on = modified_on;
      existingUserLocationRole.modified_by = modified_by;
      await this.userRoleAuthorizationRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
      savedUserLocationRoles.push(existingUserLocationRole);
    } else {
      const userLocationRole = new UserRoleAuthorization({
        user_id: user_id,
        roles: roles,
        tier1_id: locations.tier1_id,
        tier2_id: locations.tier2_id,
        tier3_id: locations.tier3_id,
        created_on,
        created_by,
        userProfileId,
      });

      const savedUserLocationRole = await this.userRoleAuthorizationRepository.create(userLocationRole);
      savedUserLocationRoles.push(savedUserLocationRole);
    }

    // Handle role adjustments based on cases
    if (locations.tier1_id === 0 && locations.tier2_id === null && locations.tier3_id === null) {
      // Case 1: Remove roles based on all other cases
      await this.adjustRoles(user_id, userProfileId, roles, {tier1_id: {neq: 0}});
    } else if (locations.tier1_id !== 0 && locations.tier2_id === 0 && locations.tier3_id === null) {
      // Case 2: Remove roles based on Case 2 & 3
      await this.adjustRoles(user_id, userProfileId, roles, {tier1_id: locations.tier1_id, tier2_id: {neq: 0}});
    } else if (locations.tier1_id !== 0 && locations.tier2_id !== 0 && locations.tier3_id === 0) {
      // Case 3: Remove roles based on Case 3
      await this.adjustRoles(user_id, userProfileId, roles, {
        tier1_id: locations.tier1_id,
        tier2_id: locations.tier2_id,
        tier3_id: {neq: 0},
      });
    } else if (
      locations.tier1_id !== 0 &&
      locations.tier2_id !== 0 &&
      locations.tier3_id !== 0
    ) {
      // Case 4: No removal needed
    }
    let repo = await this.userRoleAuthorizationRepository.find({where: {userProfileId: userProfileId, user_id: user_id}});
    return {object: savedUserLocationRoles, data: repo};
  }

  async adjustRoles(user_id: number, userProfileId: number, roles: number[], locationConditions: any) {
    const userLocationRoles = await this.userRoleAuthorizationRepository.find({
      where: {
        and: [
          {user_id: user_id},
          {userProfileId: userProfileId},
          locationConditions,
        ],
      },
    });

    for (const role of userLocationRoles) {
      if (role.roles) {
        role.roles = role.roles.filter((roleId: number) => !roles.includes(roleId));
        await this.userRoleAuthorizationRepository.updateById(role.id, role);
      }
    }
  }
  @post('/multiple-user-location-roles')
  @response(200, {
    description: 'Array of UserRoleAuthorization model instances',
    content: {'application/json': {schema: {type: 'array', items: {'x-ts-type': UserRoleAuthorization}}}},
  })
  async createMultiple(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              user_ids: {type: 'array', items: {type: 'number'}},
              roles: {type: 'array', items: {type: 'number'}},
              locations: {
                type: 'object',
                properties: {
                  tier1_id: {type: 'number', nullable: true},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
              },
              created_on: {type: 'string', nullable: true},
              created_by: {type: 'number', nullable: true},
              modified_on: {type: 'string', nullable: true},
              modified_by: {type: 'number', nullable: true},
            },
            required: ['userProfileId', 'locations', 'roles', 'user_ids'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<any> {
    const {user_ids, roles, locations, created_on, created_by, modified_on, modified_by, userProfileId} = requestBody;
    const savedUserLocationRoles: UserRoleAuthorization[] = [];

    for (const user_id of user_ids) {
      const existingUserLocationRole = await this.userRoleAuthorizationRepository.findOne({
        where: {
          and: [
            {tier1_id: locations.tier1_id},
            {tier2_id: locations.tier2_id},
            {tier3_id: locations.tier3_id},
            {userProfileId: userProfileId},
            {user_id: user_id},
          ],
        },
      });

      if (existingUserLocationRole && existingUserLocationRole.roles) {
        existingUserLocationRole.roles = Array.from(new Set([...existingUserLocationRole.roles, ...roles]));
        existingUserLocationRole.modified_on = modified_on;
        existingUserLocationRole.modified_by = modified_by;
        await this.userRoleAuthorizationRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
        savedUserLocationRoles.push(existingUserLocationRole);
      } else {
        const userLocationRole = new UserRoleAuthorization({
          user_id: user_id,
          roles: roles,
          tier1_id: locations.tier1_id,
          tier2_id: locations.tier2_id,
          tier3_id: locations.tier3_id,
          created_on,
          created_by,
          userProfileId,
        });

        const savedUserLocationRole = await this.userRoleAuthorizationRepository.create(userLocationRole);
        savedUserLocationRoles.push(savedUserLocationRole);
      }

      // Handle role adjustments based on cases
      if (locations.tier1_id === 0 && locations.tier2_id === null && locations.tier3_id === null) {
        await this.adjustRoles(user_id, userProfileId, roles, {tier1_id: {neq: 0}});
      } else if (locations.tier1_id !== 0 && locations.tier2_id === 0 && locations.tier3_id === null) {
        await this.adjustRoles(user_id, userProfileId, roles, {tier1_id: locations.tier1_id, tier2_id: {neq: 0}});
      } else if (locations.tier1_id !== 0 && locations.tier2_id !== 0 && locations.tier3_id === 0) {
        await this.adjustRoles(user_id, userProfileId, roles, {
          tier1_id: locations.tier1_id,
          tier2_id: locations.tier2_id,
          tier3_id: {neq: 0},
        });
      }
    }

    // Retrieve and return all created/updated records for each user
    const userRecords = await Promise.all(
      user_ids.map(async (user_id: number) => {
        return this.userRoleAuthorizationRepository.find({
          where: {userProfileId: userProfileId, user_id: user_id},
        });
      }),);

    return {object: savedUserLocationRoles, data: userRecords};
  }


  @post('/user-role-authorizations/get-individual-users')
  @response(200, {
    description: 'List of users under the specified locations with roles',

  })
  async getIndividualUsers(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              user_id: {
                type: 'number',
              },
              userProfileId: {
                type: 'number',
              },
            },
            required: ['userProfileId', 'locations', 'user_id'],
          },
        },
      },
    })
    requestBody: {
      roles: any[];
      user_id: number;
      locations: any;
      userProfileId: number;
    },
  ): Promise<any[]> {
    const {user_id, locations, userProfileId} = requestBody;

    // Fetch all relevant role assignments
    const roleAssignments = await this.userRoleAuthorizationRepository.find({
      where: {
        and: [
          {user_id: user_id},
          {userProfileId: userProfileId},
          {
            or: [
              {tier1_id: 0, tier2_id: null, tier3_id: null}, // Corporate level
              {tier1_id: locations.tier1_id, tier2_id: 0, tier3_id: null}, // Country level
              {tier1_id: locations.tier1_id, tier2_id: locations.tier2_id, tier3_id: 0}, // Region level
              {tier1_id: locations.tier1_id, tier2_id: locations.tier2_id, tier3_id: locations.tier3_id}, // Site level
            ],
          },
        ],
      },
    });

    // Throw an error if multiple assignments are found for the same location level
    const filteredAssignments = roleAssignments.filter(assignment =>
      assignment.tier1_id === locations.tier1_id &&
      assignment.tier2_id === locations.tier2_id &&
      assignment.tier3_id === locations.tier3_id
    );

    if (filteredAssignments.length > 1) {
      throw new Error('Found Multiple Assignments');
    }

    // Initialize roles and disabledRoles arrays
    let allRoles: any[] = [];
    let disabledRoles: any[] = [];

    // Collect roles and disabled roles based on hierarchy
    roleAssignments.forEach(assignment => {

      if ((assignment.tier1_id === locations.tier1_id && assignment.tier2_id === locations.tier2_id && assignment.tier3_id === locations.tier3_id)) {
        allRoles = allRoles.concat(assignment.roles);

        if ((assignment.tier1_id === 0 && assignment.tier2_id === null && assignment.tier3_id === null)) {

          disabledRoles = disabledRoles.concat(assignment.roles);
        }
      } else {
        disabledRoles = disabledRoles.concat(assignment.roles);
      }
    });

    allRoles = Array.from(new Set(allRoles));
    disabledRoles = Array.from(new Set(disabledRoles));

    let finalRoles = allRoles.filter(role => !disabledRoles.includes(role));
    // disabledRoles = disabledRoles.filter(role => !allRoles.includes(role));
    console.log(finalRoles)
    if (filteredAssignments.length === 0) {
      return [{
        roles: [],
        disabledRoles: disabledRoles,
      }];
    }

    return filteredAssignments.map(assignment => {
      let roles = assignment.roles || [];
      return {
        ...assignment,
        roles: roles,
        disabledRoles: (locations.tier1_id === 0 && locations.tier2_id === null && locations.tier3_id === null) ? [] : disabledRoles
      };
    });
  }
  @post('/user-role-authorizations/get-users-by-roles')
  @response(200, {
    description: 'List of users with specified roles in given locations',
  })
  async getUsersByRoles(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              locations: {
                type: 'object',
                properties: {
                  tier1_id: {type: 'number'},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
              },
              roles: {
                type: 'array',
                items: {type: 'number'},
              },
            },
            required: ['userProfileId', 'locations', 'roles'],
          },
        },
      },
    })
    requestBody: {
      userProfileId: number;
      locations: {
        tier1_id: number;
        tier2_id?: number | null;
        tier3_id?: number | null;
      };
      roles: number[];
    },
  ): Promise<any> {
    const {userProfileId, locations, roles} = requestBody;

    // Find all users assigned with the specified roles within the given locations
    const userRoleAssignments = await this.userRoleAuthorizationRepository.find({
      where: {
        and: [
          {userProfileId: userProfileId},
          {
            or: [
              {tier1_id: 0, tier2_id: null, tier3_id: null}, // Corporate level
              {tier1_id: locations.tier1_id, tier2_id: 0, tier3_id: null}, // Country level
              {tier1_id: locations.tier1_id, tier2_id: locations.tier2_id, tier3_id: 0}, // Region level
              {tier1_id: locations.tier1_id, tier2_id: locations.tier2_id, tier3_id: locations.tier3_id}, // Site level
            ],
          }
          // Filter by specific role IDs
        ],
      },
    });

    // Collect user IDs from matched role assignments
    const userIds = userRoleAssignments.filter(i => roles.every(x => i?.roles?.includes(x))).map(assignment => assignment.user_id);
    console.log(userIds)
    // Retrieve user details from userProfileRepository
    const users = await this.userProfileRepository.find({
      where: {
        id: {inq: userIds}, clientId: userProfileId
      },
    });

    return {user_ids: users.map(user => user.id)};
  }

  @del('/reset-user-role-authorizations/{id}')
  @response(204, {
    description: 'UserRoleAuthorization DELETE success',
  })
  async deleteByUserId(@param.path.number('id') id: number): Promise<void> {
    await this.userRoleAuthorizationRepository.deleteAll({user_id: id});
  }
  @post('/set-default-all-users-rra')
  @response(200, {
    description: 'List of users under the specified locations with roles',

  })
  async setDefaultAll(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              client_ids: {
                type: 'array',
                items: {
                  type: 'number',
                }
              },
              roles: {
                type: 'array',
                items: {
                  type: 'number',
                }
              }
            },
            required: ['client_ids', 'roles'],
          },
        },
      },
    })
    requestBody: {
      roles: number[];
      client_ids: number[];
    },
  ): Promise<any[]> {
    const {roles, client_ids} = requestBody
    let roles_add = [], addedRoles = []
    if (!client_ids.length) {
      let users = await this.userProfileRepository.find({where: {role: 'clientuser'}})
      for (const uid of users.filter(i => i.clientId !== null).map(i => ({id: i.id, clientId: i.clientId}))) {
        let dt = DateTime.utc().toString()
        roles_add.push({user_id: uid.id, created_by: uid.clientId, created_on: dt, userProfileId: uid.clientId, roles: roles, tier1_id: 0, tier2_id: null, tier3_id: null})
      }
      for (const ass of roles_add) {
        let old = await this.userRoleAuthorizationRepository.findOne({where: {user_id: ass.user_id, userProfileId: ass.userProfileId, tier1_id: ass.tier1_id, tier2_id: ass.tier2_id, tier3_id: ass.tier3_id}})
        if (!old) {
          addedRoles.push(await this.userRoleAuthorizationRepository.create(ass))
        } else {
          console.log(old)
        }
      }

    } else {
      let users = await this.userProfileRepository.find({where: {role: 'clientuser', clientId: {inq: client_ids}}})
      console.log(users)
      for (const uid of users.filter(i => i.clientId !== null).map(i => ({id: i.id, clientId: i.clientId}))) {
        let dt = DateTime.utc().toString()
        roles_add.push({user_id: uid.id, created_by: uid.clientId, created_on: dt, userProfileId: uid.clientId, roles: roles, tier1_id: 0, tier2_id: null, tier3_id: null})
      }
      for (const ass of roles_add) {
        let old = await this.userRoleAuthorizationRepository.findOne({where: {user_id: ass.user_id, userProfileId: ass.userProfileId, tier1_id: ass.tier1_id, tier2_id: ass.tier2_id, tier3_id: ass.tier3_id}})
        if (!old) {
          addedRoles.push(await this.userRoleAuthorizationRepository.create(ass))
        } else {
          console.log(old)
        }
      }
    }
    return addedRoles
  }

  @post('/user-role-authorizations/filter-by-roles')
  @response(200, {
    description: 'Filter UserRoleAuthorization records by roleId array',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: {type: 'number'},
                  user_id: {type: 'number'},
                  userProfileId: {type: 'number'},
                  roles: {
                    type: 'array',
                    items: {type: 'number'}
                  },
                  tier1_id: {type: 'number'},
                  tier2_id: {type: 'number'},
                  tier3_id: {type: 'number'},
                  created_on: {type: 'string'},
                  created_by: {type: 'number'},
                  modified_on: {type: 'string'},
                  modified_by: {type: 'number'}
                }
              }
            },
            total_count: {type: 'number'}
          }
        }
      }
    },
  })
  async filterByRoles(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              roleIds: {
                type: 'array',
                items: {type: 'number'},
                description: 'Array of role IDs to filter by'
              },
              userProfileId: {
                type: 'number',
                description: 'Optional userProfileId filter'
              },
              user_id: {
                type: 'number',
                description: 'Optional user_id filter'
              },
              tier1_id: {
                type: 'number',
                description: 'Optional tier1_id filter'
              },
              tier2_id: {
                type: 'number',
                description: 'Optional tier2_id filter'
              },
              tier3_id: {
                type: 'number',
                description: 'Optional tier3_id filter'
              }
            },
            required: ['roleIds']
          }
        }
      }
    })
    requestBody: {
      roleIds: number[];
      userProfileId?: number;
      user_id?: number;
      tier1_id?: number;
      tier2_id?: number;
      tier3_id?: number;
    }
  ): Promise<{status: boolean; message: string; data: any[]; total_count: number}> {
    try {
      const {roleIds, userProfileId, user_id, tier1_id, tier2_id, tier3_id} = requestBody;

      // Validate roleIds array
      if (!roleIds || roleIds.length === 0) {
        return {
          status: false,
          message: 'At least one roleId is required',
          data: [],
          total_count: 0
        };
      }

      // Build the base SQL query
      let sqlQuery = `
        SELECT * FROM UserRoleAuthorization
        WHERE roles IS NOT NULL
          AND JSON_LENGTH(roles) > 0
      `;

      // Parameters array for the SQL query
      const queryParams: any[] = [];

      // Add role filtering for each roleId using JSON_CONTAINS
      for (const roleId of roleIds) {
        sqlQuery += ` AND JSON_CONTAINS(roles, ?, '$')`;
        queryParams.push(JSON.stringify([roleId]));
      }

      // Add optional filters
      if (userProfileId !== undefined) {
        sqlQuery += ` AND userProfileId = ?`;
        queryParams.push(userProfileId);
      }

      if (user_id !== undefined) {
        sqlQuery += ` AND user_id = ?`;
        queryParams.push(user_id);
      }

      if (tier1_id !== undefined) {
        sqlQuery += ` AND tier1_id = ?`;
        queryParams.push(tier1_id);
      }

      if (tier2_id !== undefined) {
        sqlQuery += ` AND tier2_id = ?`;
        queryParams.push(tier2_id);
      }

      if (tier3_id !== undefined) {
        sqlQuery += ` AND tier3_id = ?`;
        queryParams.push(tier3_id);
      }

      // Add ordering
      sqlQuery += ` ORDER BY id DESC`;

      // Execute the SQL query
      const queryResult = await this.userRoleAuthorizationRepository.execute(
        sqlQuery,
        queryParams
      );

      // Convert result to array if it's not already
      const roles = Array.isArray(queryResult) ? queryResult : [queryResult];

      return {
        status: true,
        message: `Found ${roles.length} records matching the specified role IDs: [${roleIds.join(', ')}]`,
        data: roles,
        total_count: roles.length
      };

    } catch (error) {
      return {
        status: false,
        message: `Error filtering by roles: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: [],
        total_count: 0
      };
    }
  }


}
