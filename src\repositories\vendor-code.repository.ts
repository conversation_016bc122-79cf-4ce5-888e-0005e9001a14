import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {VendorCode, VendorCodeRelations} from '../models';

export class VendorCodeRepository extends DefaultCrudRepository<
  VendorCode,
  typeof VendorCode.prototype.id,
  VendorCodeRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(VendorCode, dataSource);
  }
}
