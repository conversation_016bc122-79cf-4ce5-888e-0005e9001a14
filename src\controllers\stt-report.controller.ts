import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, requestBody, response} from '@loopback/rest';
import {
  FormCollectionRepository,
  NewMetricRepository,
  UserProfileRepository
} from '../repositories';
import {UserProfileController} from './user-profile.controller';

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository
  ) { }


  @post('/stt-report/main-function')
  @response(200, {
    description: 'Main STT Report Function - Processes complete input and calls appropriate sub-functions',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {type: 'object'}
          }
        }
      }
    },
  })
  async mainFunction(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            main_data_source: {type: 'string', enum: ['Quantitative', 'Qualitative', 'Config']},
            sub_data_source: {type: 'string', enum: ['raw', 'indicator']},
            raw_parameters: {
              type: 'object',
              properties: {
                dcf_name: {type: 'array', items: {type: 'string'}},
                sub_status: {type: 'string', enum: ['live', 'locked']},
                breakdown: {type: 'boolean'},
                breakdown_data: {type: 'string', enum: ['dcf_name', 'data_point_name']}
              }
            },
            indicator_parameters: {
              type: 'object',
              properties: {
                indicator_name: {type: 'array', items: {type: 'string'}},
                sub_status: {type: 'string', enum: ['locked', 'breakdown']},
                breakdown_data: {type: 'string', enum: ['dcf_name']}
              }
            },
            filter_type: {type: 'string', enum: ['applied_common_filter', 'applied_specific_filter']},
            applied_common_filter: {
              type: 'object',
              properties: {
                year: {type: 'string'},
                reporting_period: {type: 'string', enum: ['Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom']},
                reporting_period_from: {type: 'string'},
                reporting_period_to: {type: 'string'},
                locked_date: {type: 'string'},
                entity: {type: 'string'}
              }
            },
            applied_specific_filter: {
              type: 'object',
              properties: {
                year: {type: 'string'},
                reporting_period: {type: 'string', enum: ['Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom']},
                reporting_period_from: {type: 'string'},
                reporting_period_to: {type: 'string'},
                locked_date: {type: 'string'},
                entity: {type: 'string'}
              }
            },
            type_of_data: {type: 'string', enum: ['queried_data', 'direct_extract']},
            type_of_format: {type: 'string', enum: ['tabular_form_data', 'value_field', 'chart_data']},
            query_details: {
              type: 'object',
              properties: {
                query_type: {type: 'string', enum: ['sum', 'ratio', 'percentage', 'count']},
                sub_query_type: {type: 'string'},
                query_parameters: {type: 'object'}
              }
            },
            table_config: {
              type: 'object',
              properties: {
                period_breakdown: {type: 'string'},
                entity_breakdown: {type: 'boolean'},
                entity_details: {type: 'string'},
                dcf_breakdown: {type: 'boolean'}
              }
            },
            chart_config: {
              type: 'object',
              properties: {
                period_breakdown: {type: 'string'},
                entity_breakdown: {type: 'boolean'},
                dcf_breakdown: {type: 'boolean'},
                entity_details: {type: 'string'}
              }
            }
          },
          required: ['main_data_source', 'sub_data_source', 'filter_type', 'type_of_data', 'type_of_format']
        }
      }
    }
  })
  requestData: any): Promise<{status: boolean; message: string; data?: any}> {

    try {
      validateInputParameters
      return {status: true, message: 'Main function executed successfully'};

    } catch (error) {
      return {
        status: false,
        message: `Error in main function: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

}
