import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, requestBody, response} from '@loopback/rest';
import {
  FormCollectionRepository,
  NewMetricRepository,
  UserProfileRepository
} from '../repositories';
import {UserProfileController} from './user-profile.controller';

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository
  ) { }

  @post('/stt-report/validate-input-parameters')
  @response(200, {
    description: 'STT Report Input Validation',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            validationType: {type: 'number'}
          }
        }
      }
    },
  })
  async validateInputParameters(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            "main_data_source": {
              type: 'string',
              enum: ['Quantitative', 'Qualitative', 'Config']
            },
            "sub_data_source": {
              type: 'string',
              enum: ['raw', 'indicator']
            },
            "raw_parameters": {
              type: 'object',
              properties: {
                "indicator_name": {type: 'array', items: {type: 'number'}},
                "dcf_name": {type: 'array', items: {type: 'number'}},
                "sub_status": {type: 'string'},
                "breakdown": {type: 'boolean'},
                "breakdown_data": {type: 'string'},


              }
            }
          },
          required: [
            'main_data_source',
            'sub_data_source',
            'raw_parameters'
          ]
        },
      },
    },
  })
  inputParameters: {
    main_data_source: string;
    sub_data_source: string;
    raw_parameters: {
      indicator_name?: number[];
      dcf_name?: number[];
      sub_status?: string;
      breakdown?: boolean;
      breakdown_data?: string;
    };
  }): Promise<{status: boolean; message: string; data?: any}> {

    const {main_data_source, sub_data_source, raw_parameters} = inputParameters;

    // Validate main data source
    const validMainDataSources = ['Quantitative', 'Qualitative', 'Config'];
    const validSubDataSources = ['raw', 'indicator'];

    if (!validMainDataSources.includes(main_data_source)) {
      return {
        status: false,
        message: `Invalid main_data_source. Must be one of: ${validMainDataSources.join(', ')}`
      };
    }

    if (!validSubDataSources.includes(sub_data_source)) {
      return {
        status: false,
        message: `Invalid sub_data_source. Must be one of: ${validSubDataSources.join(', ')}`
      };
    }

    if (!raw_parameters || Object.keys(raw_parameters).length === 0) {
      return {
        status: false,
        message: 'raw_parameters cannot be empty'
      };
    }

    // Determine validation type based on data source combination
    if (main_data_source === 'Quantitative') {
      if (sub_data_source === 'raw') {



        return {
          status: true,
          message: 'Valid parameters for Quantitative raw data'
        };
      } else if (sub_data_source === 'indicator') {
        const indicatorList = await this.userProfileController.getAssignedIndicator(94, {indicatorId: raw_parameters.indicator_name ? raw_parameters.indicator_name : []})
        return {
          status: true,
          message: 'Valid parameters for Quantitative indicator data',
          data: indicatorList
        };
      }
    } else if (main_data_source === 'Qualitative') {
      return {
        status: false,
        message: 'Not developed yet'
      };
    } else if (main_data_source === 'Config') {
      return {
        status: false,
        message: 'Not developed yet'
      };
    }

    return {
      status: false,
      message: 'Invalid combination of parameters'
    };
  }

  @post('/stt-report/generate-report')
  @response(200, {
    description: 'Generate STT Report with comprehensive data processing',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {type: 'object'}
          }
        }
      }
    },
  })
  async generateSttReport(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            main_data_source: {type: 'string', enum: ['Quantitative', 'Qualitative', 'Config']},
            sub_data_source: {type: 'string', enum: ['raw', 'indicator']},
            raw_parameters: {type: 'object'},
            indicator_parameters: {type: 'object'},
            filter_type: {type: 'string', enum: ['applied_common_filter', 'applied_specific_filter']},
            applied_common_filter: {type: 'object'},
            applied_specific_filter: {type: 'object'},
            type_of_data: {type: 'string', enum: ['queried_data', 'direct_extract']},
            type_of_format: {type: 'string', enum: ['tabular_form_data', 'value_field', 'chart_data']},
            query_details: {type: 'object'},
            table_config: {type: 'object'},
            chart_config: {type: 'object'}
          },
          required: ['main_data_source', 'sub_data_source', 'filter_type', 'type_of_data', 'type_of_format']
        }
      }
    }
  })
  requestData: any): Promise<{status: boolean; message: string; data?: any}> {

    try {
      // Step 1: Validate Input Parameters
      const validationResult = await this.validateSttInputParameters(requestData);
      if (!validationResult.status) {
        return validationResult;
      }

      // Step 2: Load Form Configuration
      const formConfig = await this.loadFormConfiguration(requestData);
      if (!formConfig.status) {
        return formConfig;
      }

      // Step 3: Resolve Reporting Frequency
      const frequencyResult = await this.resolveReportingFrequency(requestData);
      if (!frequencyResult.status) {
        return frequencyResult;
      }

      // Step 4: Select Frequency Handler
      const handlerResult = await this.selectFrequencyHandler(requestData, frequencyResult.data);
      if (!handlerResult.status) {
        return handlerResult;
      }

      // Step 5: Fetch Data from Data Layer
      const dataResult = await this.fetchDataFromDataLayer(requestData, formConfig.data);
      if (!dataResult.status) {
        return dataResult;
      }

      // Step 6: Apply Query Processor
      const queryResult = await this.applyQueryProcessor(requestData, dataResult.data);
      if (!queryResult.status) {
        return queryResult;
      }

      // Step 7: Invoke Granularity Engine
      const granularityResult = await this.invokeGranularityEngine(requestData, queryResult.data);
      if (!granularityResult.status) {
        return granularityResult;
      }

      // Step 8: Format and Validate Output
      const formatResult = await this.formatAndValidateOutput(requestData, granularityResult.data);
      if (!formatResult.status) {
        return formatResult;
      }

      // Step 9: Return Final Result
      return this.returnFinalResult(formatResult.data);

    } catch (error) {
      return {
        status: false,
        message: `Error generating STT report: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Private helper functions for STT report processing

  /**
   * Step 1: Parse Input
   * Parse the incoming JSON object for the report data mapping and extract fields
   */
  private parseInput(requestData: any): {status: boolean; message: string; data?: any} {
    try {
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        query_details,
        table_config,
        chart_config
      } = requestData;

      return {
        status: true,
        message: 'Input parsed successfully',
        data: {
          main_data_source,
          sub_data_source,
          raw_parameters,
          indicator_parameters,
          filter_type,
          applied_common_filter,
          applied_specific_filter,
          type_of_data,
          type_of_format,
          query_details,
          table_config,
          chart_config
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Input parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 2: Validate Base Parameters
   * Ensure all mandatory fields based on the options selected are present and not null
   */
  private validateBaseParameters(parsedData: any): {status: boolean; message: string; data?: any} {
    try {
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format
      } = parsedData;

      // Validate main_data_source
      if (!main_data_source || !['Quantitative', 'Qualitative', 'Config'].includes(main_data_source)) {
        return {
          status: false,
          message: 'main_data_source is mandatory and must be Quantitative, Qualitative, or Config'
        };
      }

      // Validate sub_data_source
      if (!sub_data_source || !['raw', 'indicator'].includes(sub_data_source)) {
        return {
          status: false,
          message: 'sub_data_source is mandatory and must be raw or indicator'
        };
      }

      // Validate parameters based on sub_data_source
      if (sub_data_source === 'raw') {
        if (!raw_parameters) {
          return {
            status: false,
            message: 'raw_parameters is mandatory when sub_data_source is raw'
          };
        }

        // dcf_name is mandatory for raw data source
        if (!raw_parameters.dcf_name || !Array.isArray(raw_parameters.dcf_name) || raw_parameters.dcf_name.length === 0) {
          return {
            status: false,
            message: 'dcf_name is mandatory and must be a non-empty array in raw_parameters'
          };
        }
      }

      if (sub_data_source === 'indicator') {
        if (!indicator_parameters) {
          return {
            status: false,
            message: 'indicator_parameters is mandatory when sub_data_source is indicator'
          };
        }

        // indicator_name is mandatory for indicator data source
        if (!indicator_parameters.indicator_name || !Array.isArray(indicator_parameters.indicator_name) || indicator_parameters.indicator_name.length === 0) {
          return {
            status: false,
            message: 'indicator_name is mandatory and must be a non-empty array in indicator_parameters'
          };
        }
      }

      // Validate filter_type
      if (!filter_type || !['applied_common_filter', 'applied_specific_filter'].includes(filter_type)) {
        return {
          status: false,
          message: 'filter_type is mandatory and must be applied_common_filter or applied_specific_filter'
        };
      }

      // Validate filter data based on filter_type
      const filterData = filter_type === 'applied_common_filter' ? applied_common_filter : applied_specific_filter;
      if (!filterData) {
        return {
          status: false,
          message: `${filter_type} data is mandatory when filter_type is ${filter_type}`
        };
      }

      // Validate mandatory filter fields
      if (!filterData.entity) {
        return {
          status: false,
          message: 'entity is mandatory in filter data'
        };
      }

      if (!filterData.reporting_period_from || !filterData.reporting_period_to) {
        return {
          status: false,
          message: 'reporting_period_from and reporting_period_to are mandatory in filter data'
        };
      }

      // Validate type_of_data
      if (!type_of_data || !['queried_data', 'direct_extract'].includes(type_of_data)) {
        return {
          status: false,
          message: 'type_of_data is mandatory and must be queried_data or direct_extract'
        };
      }

      // Validate type_of_format
      if (!type_of_format || !['tabular_form_data', 'value_field', 'chart_data'].includes(type_of_format)) {
        return {
          status: false,
          message: 'type_of_format is mandatory and must be tabular_form_data, value_field, or chart_data'
        };
      }

      return {
        status: true,
        message: 'Base parameters validated successfully',
        data: parsedData
      };

    } catch (error) {
      return {
        status: false,
        message: `Base parameter validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 3: Validation for direct_extract
   * Validates configuration existence for direct extract scenarios
   */
  private async validateDirectExtract(validatedData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {sub_data_source, raw_parameters, indicator_parameters, type_of_data, filter_type, applied_common_filter, applied_specific_filter} = validatedData;

      if (type_of_data !== 'direct_extract') {
        return {
          status: true,
          message: 'Direct extract validation skipped - not direct_extract type',
          data: validatedData
        };
      }

      const filterData = filter_type === 'applied_common_filter' ? applied_common_filter : applied_specific_filter;
      const entity = filterData.entity;
      const reportingPeriodFrom = filterData.reporting_period_from;
      const reportingPeriodTo = filterData.reporting_period_to;

      if (sub_data_source === 'raw') {
        // Step 3.a: If data_source is raw
        const dcfNames = raw_parameters.dcf_name;

        for (const dcfName of dcfNames) {
          // Check in Configuration Layer if configuration exists for this combination
          const configExists = await this.checkConfigurationExists(dcfName, entity, reportingPeriodFrom, reportingPeriodTo);

          if (!configExists.status) {
            return {
              status: false,
              message: `Configuration not found for DCF: ${dcfName}, entity: ${entity}, period: ${reportingPeriodFrom} to ${reportingPeriodTo}`
            };
          }

          // Get the frequency configured and resolve reporting frequency
          const frequencyResult = await this.resolveReportingFrequency(validatedData);
          if (!frequencyResult.status) {
            return frequencyResult;
          }
        }

        return {
          status: true,
          message: 'Direct extract validation completed for raw data source',
          data: validatedData
        };
      }

      if (sub_data_source === 'indicator') {
        // Step 3.b: If data_source is indicator
        const indicatorNames = indicator_parameters.indicator_name;

        for (const indicatorName of indicatorNames) {
          // Retrieve the list of DCF forms associated with the indicator
          const associatedDcfs = await this.getAssociatedDcfForIndicator(indicatorName);

          if (!associatedDcfs.status || !associatedDcfs.data || associatedDcfs.data.length === 0) {
            return {
              status: false,
              message: `No associated DCF forms found for indicator: ${indicatorName}`
            };
          }

          // For each associated DCF form, repeat step 3.a
          for (const dcfName of associatedDcfs.data) {
            const configExists = await this.checkConfigurationExists(dcfName, entity, reportingPeriodFrom, reportingPeriodTo);

            if (!configExists.status) {
              return {
                status: false,
                message: `Configuration not found for associated DCF: ${dcfName} (from indicator: ${indicatorName}), entity: ${entity}, period: ${reportingPeriodFrom} to ${reportingPeriodTo}`
              };
            }
          }
        }

        return {
          status: true,
          message: 'Direct extract validation completed for indicator data source',
          data: validatedData
        };
      }

      return {
        status: false,
        message: 'Invalid sub_data_source for direct extract validation'
      };

    } catch (error) {
      return {
        status: false,
        message: `Direct extract validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Updated main validation function that combines all steps
   */
  private async validateSttInputParameters(requestData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Step 1: Parse Input
      const parseResult = this.parseInput(requestData);
      if (!parseResult.status) {
        return parseResult;
      }

      // Step 2: Validate Base Parameters
      const baseValidationResult = this.validateBaseParameters(parseResult.data);
      if (!baseValidationResult.status) {
        return baseValidationResult;
      }

      // Step 3: Validation for direct_extract
      const directExtractResult = await this.validateDirectExtract(baseValidationResult.data);
      if (!directExtractResult.status) {
        return directExtractResult;
      }

      return {
        status: true,
        message: 'All input validation steps completed successfully',
        data: directExtractResult.data
      };

    } catch (error) {
      return {
        status: false,
        message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 2: Load Form Configuration
   * Loads the configuration and structure for the specified form from FormCollection
   */
  private async loadFormConfiguration(requestData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {sub_data_source, raw_parameters, indicator_parameters} = requestData;

      if (sub_data_source === 'raw' && raw_parameters?.dcf_name) {
        // Load DCF configuration from FormCollection
        const dcfConfigs = [];

        for (const dcfName of raw_parameters.dcf_name) {
          const formConfig = await this.formCollectionRepository.findOne({
            where: {name: dcfName}
          });

          if (!formConfig) {
            return {
              status: false,
              message: `DCF configuration not found for: ${dcfName}`
            };
          }

          dcfConfigs.push(formConfig);
        }

        return {
          status: true,
          message: 'DCF configurations loaded successfully',
          data: {
            type: 'dcf',
            configurations: dcfConfigs
          }
        };
      }

      if (sub_data_source === 'indicator' && indicator_parameters?.indicator_name) {
        // Load indicator configuration from NewMetric
        const indicatorConfigs = [];

        for (const indicatorName of indicator_parameters.indicator_name) {
          const indicatorConfig = await this.newMetricRepository.findOne({
            where: {name: indicatorName}
          });

          if (!indicatorConfig) {
            return {
              status: false,
              message: `Indicator configuration not found for: ${indicatorName}`
            };
          }

          indicatorConfigs.push(indicatorConfig);
        }

        return {
          status: true,
          message: 'Indicator configurations loaded successfully',
          data: {
            type: 'indicator',
            configurations: indicatorConfigs
          }
        };
      }

      return {
        status: false,
        message: 'No valid configuration parameters found'
      };

    } catch (error) {
      return {
        status: false,
        message: `Configuration loading error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 3: Resolve Reporting Frequency
   * Determines the reporting frequency for the given form and period
   */
  private async resolveReportingFrequency(requestData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const frequency_list = [
        {name: 'Monthly', id: 1},
        {name: 'Bi-Monthly', id: 2},
        {name: 'Quarterly', id: 3},
        {name: 'Annually', id: 4},
        {name: 'Bi-Annually', id: 5}
      ];

      const filterData = requestData.applied_common_filter || requestData.applied_specific_filter;

      if (!filterData || !filterData.reporting_period) {
        return {
          status: false,
          message: 'Reporting period not specified in filter data'
        };
      }

      const reportingPeriod = filterData.reporting_period;
      let frequencyId = 1; // Default to Monthly

      // Map reporting period to frequency ID
      switch (reportingPeriod.toLowerCase()) {
        case 'monthly':
          frequencyId = 1;
          break;
        case 'bi-monthly':
          frequencyId = 2;
          break;
        case 'quarterly':
          frequencyId = 3;
          break;
        case 'annually':
        case 'yearly':
          frequencyId = 4;
          break;
        case 'bi-annually':
        case 'half-yearly':
          frequencyId = 5;
          break;
        default:
          frequencyId = 1; // Default to monthly
      }

      const selectedFrequency = frequency_list.find(f => f.id === frequencyId);

      return {
        status: true,
        message: 'Reporting frequency resolved successfully',
        data: {
          frequency: selectedFrequency,
          reporting_period: reportingPeriod,
          frequency_id: frequencyId
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Frequency resolution error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 4: Select Frequency Handler
   * Routes the request to the appropriate frequency-specific handler
   */
  private async selectFrequencyHandler(requestData: any, frequencyData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {frequency_id} = frequencyData;

      let handlerType = '';
      switch (frequency_id) {
        case 1: // Monthly
          handlerType = 'monthly';
          break;
        case 2: // Bi-Monthly
          handlerType = 'bi-monthly';
          break;
        case 3: // Quarterly
          handlerType = 'quarterly';
          break;
        case 4: // Annually
          handlerType = 'yearly';
          break;
        case 5: // Bi-Annually
          handlerType = 'bi-yearly';
          break;
        default:
          handlerType = 'monthly';
      }

      return {
        status: true,
        message: `Frequency handler selected: ${handlerType}`,
        data: {
          handler_type: handlerType,
          frequency_data: frequencyData
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Handler selection error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 5: Fetch Data from Data Layer
   * Fetches raw data or indicator values from the data access layer
   */
  private async fetchDataFromDataLayer(requestData: any, formConfig: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {sub_data_source, raw_parameters, indicator_parameters} = requestData;

      if (sub_data_source === 'raw' && raw_parameters) {
        // Fetch DCF assignment data using UserProfileController
        const dcfAssignments = await this.userProfileController.getAssignedIndicatorList(94);

        return {
          status: true,
          message: 'Raw DCF data fetched successfully',
          data: {
            type: 'raw',
            dcf_data: dcfAssignments,
            form_configs: formConfig.configurations
          }
        };
      }

      if (sub_data_source === 'indicator' && indicator_parameters) {
        // Get indicator IDs from indicator names
        const indicatorIds = formConfig.configurations.map((config: any) => config.id);

        // Fetch indicator data using UserProfileController
        const indicatorData = await this.userProfileController.getAssignedIndicator(94, {
          indicatorId: indicatorIds
        });

        // Fetch quantitative data using getIndicatorDataApprovalIndicators
        const quantitativeData = await this.userProfileController.getIndicatorDataApprovalIndicators(94, {
          indicators: indicatorIds
        });

        return {
          status: true,
          message: 'Indicator data fetched successfully',
          data: {
            type: 'indicator',
            indicator_assignments: indicatorData,
            quantitative_data: quantitativeData,
            form_configs: formConfig.configurations
          }
        };
      }

      return {
        status: false,
        message: 'Invalid data source configuration'
      };

    } catch (error) {
      return {
        status: false,
        message: `Data fetching error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 6: Apply Query Processor
   * Invokes the query processor module for data aggregation
   */
  private async applyQueryProcessor(requestData: any, fetchedData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {type_of_data, query_details} = requestData;

      if (type_of_data === 'direct_extract') {
        // Return data as-is for direct extract
        return {
          status: true,
          message: 'Direct data extraction completed',
          data: fetchedData
        };
      }

      if (type_of_data === 'queried_data' && query_details) {
        const {query_type, sub_query_type} = query_details;

        let processedData = fetchedData;

        // Apply query processing based on query_type
        switch (query_type?.toLowerCase()) {
          case 'sum':
            processedData = this.applySumQuery(fetchedData, sub_query_type);
            break;
          case 'count':
            processedData = this.applyCountQuery(fetchedData, sub_query_type);
            break;
          case 'ratio':
            processedData = this.applyRatioQuery(fetchedData, sub_query_type);
            break;
          case 'percentage':
            processedData = this.applyPercentageQuery(fetchedData, sub_query_type);
            break;
          default:
            processedData = fetchedData; // No processing
        }

        return {
          status: true,
          message: `Query processing completed: ${query_type}`,
          data: processedData
        };
      }

      return {
        status: true,
        message: 'No query processing required',
        data: fetchedData
      };

    } catch (error) {
      return {
        status: false,
        message: `Query processing error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 7: Invoke Granularity Engine
   * Applies detailed breakdowns if required
   */
  private async invokeGranularityEngine(requestData: any, processedData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {raw_parameters, indicator_parameters, sub_data_source} = requestData;

      const parameters = sub_data_source === 'raw' ? raw_parameters : indicator_parameters;

      if (parameters?.breakdown && parameters?.breakdown_data) {
        // Apply breakdown logic based on breakdown_data
        const breakdownType = parameters.breakdown_data;

        let granularData = processedData;

        if (breakdownType === 'dcf_name') {
          granularData = this.applyDcfBreakdown(processedData);
        } else if (breakdownType === 'data_point_name') {
          granularData = this.applyDataPointBreakdown(processedData);
        }

        return {
          status: true,
          message: `Granularity applied: ${breakdownType}`,
          data: granularData
        };
      }

      return {
        status: true,
        message: 'No granularity breakdown required',
        data: processedData
      };

    } catch (error) {
      return {
        status: false,
        message: `Granularity processing error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 8: Format and Validate Output
   * Formats data based on output type requirements
   */
  private async formatAndValidateOutput(requestData: any, granularData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {type_of_format, table_config, chart_config} = requestData;

      let formattedData = granularData;

      switch (type_of_format) {
        case 'value_field':
          formattedData = this.formatAsValueField(granularData);
          break;
        case 'tabular_form_data':
          formattedData = this.formatAsTable(granularData, table_config);
          break;
        case 'chart_data':
          formattedData = this.formatAsChart(granularData, chart_config);
          break;
        default:
          formattedData = granularData;
      }

      return {
        status: true,
        message: `Data formatted as: ${type_of_format}`,
        data: formattedData
      };

    } catch (error) {
      return {
        status: false,
        message: `Output formatting error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 9: Return Final Result
   * Returns the final formatted result
   */
  private returnFinalResult(formattedData: any): {status: boolean; message: string; data: any} {
    return {
      status: true,
      message: 'STT report generated successfully',
      data: {
        report_data: formattedData,
        generated_at: new Date().toISOString(),
        report_type: 'STT_QUANTITATIVE_REPORT'
      }
    };
  }

  // Utility functions for query processing

  private applySumQuery(data: any, subQueryType?: string): any {
    // Implementation for sum aggregation
    if (Array.isArray(data)) {
      return data.reduce((sum, item) => {
        const value = parseFloat(item.value || item.amount || 0);
        return sum + (isNaN(value) ? 0 : value);
      }, 0);
    }
    return data;
  }

  private applyCountQuery(data: any, subQueryType?: string): any {
    // Implementation for count aggregation
    if (Array.isArray(data)) {
      return data.length;
    }
    return 1;
  }

  private applyRatioQuery(data: any, subQueryType?: string): any {
    // Implementation for ratio calculation
    if (Array.isArray(data) && data.length >= 2) {
      const numerator = parseFloat(data[0]?.value || 0);
      const denominator = parseFloat(data[1]?.value || 1);
      return denominator !== 0 ? numerator / denominator : 0;
    }
    return data;
  }

  private applyPercentageQuery(data: any, subQueryType?: string): any {
    // Implementation for percentage calculation
    const ratio = this.applyRatioQuery(data, subQueryType);
    return typeof ratio === 'number' ? ratio * 100 : data;
  }

  // Utility functions for granularity breakdown

  private applyDcfBreakdown(data: any): any {
    // Group data by DCF name
    if (Array.isArray(data)) {
      const grouped = data.reduce((acc, item) => {
        const dcfName = item.dcf_name || item.form_name || 'Unknown';
        if (!acc[dcfName]) {
          acc[dcfName] = [];
        }
        acc[dcfName].push(item);
        return acc;
      }, {});
      return grouped;
    }
    return data;
  }

  private applyDataPointBreakdown(data: any): any {
    // Group data by data point name
    if (Array.isArray(data)) {
      const grouped = data.reduce((acc, item) => {
        const dataPointName = item.data_point_name || item.metric_name || 'Unknown';
        if (!acc[dataPointName]) {
          acc[dataPointName] = [];
        }
        acc[dataPointName].push(item);
        return acc;
      }, {});
      return grouped;
    }
    return data;
  }

  // Utility functions for output formatting

  private formatAsValueField(data: any): any {
    // Format data as simple value field
    if (Array.isArray(data)) {
      return {
        total_records: data.length,
        summary_value: data.reduce((sum, item) => sum + (parseFloat(item.value || 0) || 0), 0),
        data_points: data
      };
    }
    return {value: data};
  }

  private formatAsTable(data: any, tableConfig?: any): any {
    // Format data as table structure
    if (!Array.isArray(data)) {
      data = [data];
    }

    const headers = data.length > 0 ? Object.keys(data[0]) : [];

    return {
      headers: headers,
      rows: data,
      total_rows: data.length,
      config: tableConfig || {}
    };
  }

  private formatAsChart(data: any, chartConfig?: any): any {
    // Format data for chart visualization
    if (!Array.isArray(data)) {
      data = [data];
    }

    const chartData = data.map((item, index) => ({
      x: item.period || item.date || index,
      y: parseFloat(item.value || item.amount || 0) || 0,
      label: item.name || item.label || `Data ${index + 1}`
    }));

    return {
      chart_type: chartConfig?.chart_type || 'line',
      data: chartData,
      config: chartConfig || {}
    };
  }

  // Helper functions for configuration validation

  /**
   * Check if configuration exists for the given DCF, entity, and reporting period
   */
  private async checkConfigurationExists(dcfName: string, entity: string, reportingPeriodFrom: string, reportingPeriodTo: string): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Check if DCF configuration exists in FormCollection
      const formConfig = await this.formCollectionRepository.findOne({
        where: {name: dcfName}
      });

      if (!formConfig) {
        return {
          status: false,
          message: `DCF configuration not found for: ${dcfName}`
        };
      }

      // Check if entity has access to this DCF using UserProfileController
      const dcfAssignments = await this.userProfileController.getAssignedIndicatorList(94);

      // Validate entity access and reporting period compatibility
      // This is a simplified check - you may need to add more specific logic
      const hasAccess = dcfAssignments && Array.isArray(dcfAssignments) && dcfAssignments.length > 0;

      if (!hasAccess) {
        return {
          status: false,
          message: `Entity ${entity} does not have access to DCF: ${dcfName}`
        };
      }

      return {
        status: true,
        message: 'Configuration exists and is accessible',
        data: {
          form_config: formConfig,
          entity: entity,
          reporting_period: {from: reportingPeriodFrom, to: reportingPeriodTo}
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Configuration check error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get associated DCF forms for a given indicator
   */
  private async getAssociatedDcfForIndicator(indicatorName: string): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Find the indicator configuration
      const indicatorConfig = await this.newMetricRepository.findOne({
        where: {name: indicatorName}
      });

      if (!indicatorConfig) {
        return {
          status: false,
          message: `Indicator not found: ${indicatorName}`
        };
      }

      // Get indicator assignments using UserProfileController
      const indicatorData = await this.userProfileController.getAssignedIndicator(94, {
        indicatorId: [indicatorConfig.id]
      });

      if (!indicatorData || !Array.isArray(indicatorData) || indicatorData.length === 0) {
        return {
          status: false,
          message: `No assignments found for indicator: ${indicatorName}`
        };
      }

      // Extract associated DCF names from the indicator data
      // This logic may need to be adjusted based on your actual data structure
      const associatedDcfs: string[] = [];

      for (const assignment of indicatorData) {
        if (assignment.dcf_name || assignment.form_name) {
          const dcfName = assignment.dcf_name || assignment.form_name;
          if (!associatedDcfs.includes(dcfName)) {
            associatedDcfs.push(dcfName);
          }
        }
      }

      if (associatedDcfs.length === 0) {
        return {
          status: false,
          message: `No associated DCF forms found for indicator: ${indicatorName}`
        };
      }

      return {
        status: true,
        message: `Found ${associatedDcfs.length} associated DCF forms for indicator: ${indicatorName}`,
        data: associatedDcfs
      };

    } catch (error) {
      return {
        status: false,
        message: `Error getting associated DCFs: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
