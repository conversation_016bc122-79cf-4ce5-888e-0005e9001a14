import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, requestBody, response} from '@loopback/rest';
import {
  FormCollectionRepository,
  NewMetricRepository,
  UserProfileRepository
} from '../repositories';
import {UserProfileController} from './user-profile.controller';

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository
  ) { }

  @post('/stt-report/validate-input-parameters')
  @response(200, {
    description: 'STT Report Input Validation - 3-step validation process',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {type: 'object'}
          }
        }
      }
    },
  })
  async validateInputParameters(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            main_data_source: {type: 'string', enum: ['Quantitative', 'Qualitative', 'Config']},
            sub_data_source: {type: 'string', enum: ['raw', 'indicator']},
            raw_parameters: {type: 'object'},
            indicator_parameters: {type: 'object'},
            filter_type: {type: 'string', enum: ['applied_common_filter', 'applied_specific_filter']},
            applied_common_filter: {type: 'object'},
            applied_specific_filter: {type: 'object'},
            type_of_data: {type: 'string', enum: ['queried_data', 'direct_extract']},
            type_of_format: {type: 'string', enum: ['tabular_form_data', 'value_field', 'chart_data']},
            query_details: {type: 'object'},
            table_config: {type: 'object'},
            chart_config: {type: 'object'}
          },
          required: ['main_data_source', 'sub_data_source']
        }
      }
    }
  })
  inputParameters: any): Promise<{status: boolean; message: string; data?: any}> {

    try {
      // Destructure the input parameters for flexible usage
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        query_details,
        table_config,
        chart_config
      } = inputParameters;

      // Step 1: Parse Input - Parse the incoming JSON object for the report data mapping
      const parseResult = this.parseInputStep1({
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        query_details,
        table_config,
        chart_config
      });

      if (!parseResult.status) {
        return parseResult;
      }

      // Step 2: Validate Base Parameters - Pass only required parameters
      const baseValidationResult = this.validateBaseParametersStep2({
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter
      });

      if (!baseValidationResult.status) {
        return baseValidationResult;
      }

      // Step 3: Validation for direct_extract (only if type_of_data is direct_extract)
      if (type_of_data === 'direct_extract') {
        const directExtractResult = await this.validateDirectExtractStep3({
          sub_data_source,
          raw_parameters,
          indicator_parameters,
          filter_type,
          applied_common_filter,
          applied_specific_filter
        });

        if (!directExtractResult.status) {
          return directExtractResult;
        }

        return {
          status: true,
          message: 'All 3 validation steps completed successfully (including direct_extract validation)',
          data: directExtractResult.data
        };
      }

      return {
        status: true,
        message: 'Steps 1 & 2 validation completed successfully',
        data: baseValidationResult.data
      };

    } catch (error) {
      return {
        status: false,
        message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  @post('/stt-report/main-function')
  @response(200, {
    description: 'Main STT Report Function - Processes complete input and calls appropriate sub-functions',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {type: 'object'}
          }
        }
      }
    },
  })
  async mainFunction(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            main_data_source: {type: 'string', enum: ['Quantitative', 'Qualitative', 'Config']},
            sub_data_source: {type: 'string', enum: ['raw', 'indicator']},
            raw_parameters: {
              type: 'object',
              properties: {
                dcf_name: {type: 'array', items: {type: 'string'}},
                sub_status: {type: 'string', enum: ['live', 'locked']},
                breakdown: {type: 'boolean'},
                breakdown_data: {type: 'string', enum: ['dcf_name', 'data_point_name']}
              }
            },
            indicator_parameters: {
              type: 'object',
              properties: {
                indicator_name: {type: 'array', items: {type: 'string'}},
                sub_status: {type: 'string', enum: ['locked', 'breakdown']},
                breakdown_data: {type: 'string', enum: ['dcf_name']}
              }
            },
            filter_type: {type: 'string', enum: ['applied_common_filter', 'applied_specific_filter']},
            applied_common_filter: {
              type: 'object',
              properties: {
                year: {type: 'string'},
                reporting_period: {type: 'string', enum: ['Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom']},
                reporting_period_from: {type: 'string'},
                reporting_period_to: {type: 'string'},
                locked_date: {type: 'string'},
                entity: {type: 'string'}
              }
            },
            applied_specific_filter: {
              type: 'object',
              properties: {
                year: {type: 'string'},
                reporting_period: {type: 'string', enum: ['Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom']},
                reporting_period_from: {type: 'string'},
                reporting_period_to: {type: 'string'},
                locked_date: {type: 'string'},
                entity: {type: 'string'}
              }
            },
            type_of_data: {type: 'string', enum: ['queried_data', 'direct_extract']},
            type_of_format: {type: 'string', enum: ['tabular_form_data', 'value_field', 'chart_data']},
            query_details: {
              type: 'object',
              properties: {
                query_type: {type: 'string', enum: ['sum', 'ratio', 'percentage', 'count']},
                sub_query_type: {type: 'string'},
                query_parameters: {type: 'object'}
              }
            },
            table_config: {
              type: 'object',
              properties: {
                period_breakdown: {type: 'string'},
                entity_breakdown: {type: 'boolean'},
                entity_details: {type: 'string'},
                dcf_breakdown: {type: 'boolean'}
              }
            },
            chart_config: {
              type: 'object',
              properties: {
                period_breakdown: {type: 'string'},
                entity_breakdown: {type: 'boolean'},
                dcf_breakdown: {type: 'boolean'},
                entity_details: {type: 'string'}
              }
            }
          },
          required: ['main_data_source', 'sub_data_source', 'filter_type', 'type_of_data', 'type_of_format']
        }
      }
    }
  })
  requestData: any): Promise<{status: boolean; message: string; data?: any}> {

    try {

      return {status: true, message: 'Main function executed successfully'};

    } catch (error) {
      return {
        status: false,
        message: `Error in main function: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Private helper functions for STT report processing

  /**
   * Step 1: Parse Input
   * Parse the incoming JSON object for the report data mapping and extract fields
   */
  private parseInputStep1(params: {
    main_data_source: string;
    sub_data_source: string;
    raw_parameters?: any;
    indicator_parameters?: any;
    filter_type?: string;
    applied_common_filter?: any;
    applied_specific_filter?: any;
    type_of_data?: string;
    type_of_format?: string;
    query_details?: any;
    table_config?: any;
    chart_config?: any;
  }): {status: boolean; message: string; data?: any} {
    try {
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        query_details,
        table_config,
        chart_config
      } = params;

      return {
        status: true,
        message: 'Input parsed successfully',
        data: {
          main_data_source,
          sub_data_source,
          raw_parameters,
          indicator_parameters,
          filter_type,
          applied_common_filter,
          applied_specific_filter,
          type_of_data,
          type_of_format,
          query_details,
          table_config,
          chart_config
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Input parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 2: Validate Base Parameters
   * Ensure all mandatory fields based on the options selected are present and not null
   */
  private validateBaseParametersStep2(params: {
    main_data_source: string;
    sub_data_source: string;
    raw_parameters?: any;
    indicator_parameters?: any;
    filter_type?: string;
    applied_common_filter?: any;
    applied_specific_filter?: any;
  }): {status: boolean; message: string; data?: any} {
    try {
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter
      } = params;

      // Always validate core required fields
      if (!main_data_source || !['Quantitative', 'Qualitative', 'Config'].includes(main_data_source)) {
        return {
          status: false,
          message: 'main_data_source is mandatory and must be Quantitative, Qualitative, or Config'
        };
      }

      if (!sub_data_source || !['raw', 'indicator'].includes(sub_data_source)) {
        return {
          status: false,
          message: 'sub_data_source is mandatory and must be raw or indicator'
        };
      }

      // Validate parameters based on sub_data_source (conditional validation)
      if (sub_data_source === 'raw') {
        if (!raw_parameters) {
          return {
            status: false,
            message: 'raw_parameters is mandatory when sub_data_source is raw'
          };
        }

        // dcf_name is mandatory for raw data source
        if (!raw_parameters.dcf_name || !Array.isArray(raw_parameters.dcf_name) || raw_parameters.dcf_name.length === 0) {
          return {
            status: false,
            message: 'dcf_name is mandatory and must be a non-empty array in raw_parameters'
          };
        }
      }

      if (sub_data_source === 'indicator') {
        if (!indicator_parameters) {
          return {
            status: false,
            message: 'indicator_parameters is mandatory when sub_data_source is indicator'
          };
        }

        // indicator_name is mandatory for indicator data source
        if (!indicator_parameters.indicator_name || !Array.isArray(indicator_parameters.indicator_name) || indicator_parameters.indicator_name.length === 0) {
          return {
            status: false,
            message: 'indicator_name is mandatory and must be a non-empty array in indicator_parameters'
          };
        }
      }

      // Validate filter_type only if provided
      if (filter_type && !['applied_common_filter', 'applied_specific_filter'].includes(filter_type)) {
        return {
          status: false,
          message: 'filter_type must be applied_common_filter or applied_specific_filter when provided'
        };
      }

      // Validate filter data only if filter_type is provided
      if (filter_type) {
        const filterData = filter_type === 'applied_common_filter' ? applied_common_filter : applied_specific_filter;
        if (!filterData) {
          return {
            status: false,
            message: `${filter_type} data is required when filter_type is ${filter_type}`
          };
        }

        // Only validate entity and reporting periods if filter data exists
        if (!filterData.entity) {
          return {
            status: false,
            message: 'entity is mandatory in filter data'
          };
        }

        if (!filterData.reporting_period_from || !filterData.reporting_period_to) {
          return {
            status: false,
            message: 'reporting_period_from and reporting_period_to are mandatory in filter data'
          };
        }
      }

      return {
        status: true,
        message: 'Base parameters validated successfully',
        data: params
      };

    } catch (error) {
      return {
        status: false,
        message: `Base parameter validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Step 3: Validation for direct_extract
   * Validates configuration existence for direct extract scenarios
   */
  private async validateDirectExtractStep3(params: {
    sub_data_source: string;
    raw_parameters?: any;
    indicator_parameters?: any;
    filter_type: string;
    applied_common_filter?: any;
    applied_specific_filter?: any;
  }): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const {
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter
      } = params;

      const filterData = filter_type === 'applied_common_filter' ? applied_common_filter : applied_specific_filter;
      const entity = filterData.entity;
      const reportingPeriodFrom = filterData.reporting_period_from;
      const reportingPeriodTo = filterData.reporting_period_to;

      if (sub_data_source === 'raw') {
        // Step 3.a: If data_source is raw
        const dcfNames = raw_parameters.dcf_name;

        for (const dcfName of dcfNames) {
          // Extract dcf_name from the input
          // For the given dcf_name, entity, and reporting period from and to
          // Check in the Configuration Layer if a configuration exists for this combination
          const configExists = await this.checkConfigurationExists(dcfName, entity, reportingPeriodFrom, reportingPeriodTo);

          if (!configExists.status) {
            return {
              status: false,
              message: `Configuration not found for DCF: ${dcfName}, entity: ${entity}, period: ${reportingPeriodFrom} to ${reportingPeriodTo}`
            };
          }

          // If config exists: Get the frequency configured and call resolveReportingFrequency()
          const frequencyResult = await this.resolveReportingFrequencyDummy(configExists.data);
          if (!frequencyResult.status) {
            return frequencyResult;
          }
        }

        return {
          status: true,
          message: 'Direct extract validation completed for raw data source',
          data: params
        };
      }

      if (sub_data_source === 'indicator') {
        // Step 3.b: If data_source is indicator
        const indicatorNames = indicator_parameters.indicator_name;

        for (const indicatorName of indicatorNames) {
          // Retrieve the list of DCF forms associated with the indicator using getAssignedIndicator
          const associatedDcfs = await this.getAssociatedDcfForIndicatorUsingController(indicatorName);

          if (!associatedDcfs.status || !associatedDcfs.data || associatedDcfs.data.length === 0) {
            return {
              status: false,
              message: `No associated DCF forms found for indicator: ${indicatorName}`
            };
          }

          // For each associated DCF form: Repeat step 3.a
          for (const dcfName of associatedDcfs.data) {
            const configExists = await this.checkConfigurationExists(dcfName, entity, reportingPeriodFrom, reportingPeriodTo);

            if (!configExists.status) {
              return {
                status: false,
                message: `Configuration not found for associated DCF: ${dcfName} (from indicator: ${indicatorName}), entity: ${entity}, period: ${reportingPeriodFrom} to ${reportingPeriodTo}`
              };
            }

            // Get the frequency configured and call resolveReportingFrequency()
            const frequencyResult = await this.resolveReportingFrequencyDummy(configExists.data);
            if (!frequencyResult.status) {
              return frequencyResult;
            }
          }
        }

        return {
          status: true,
          message: 'Direct extract validation completed for indicator data source',
          data: params
        };
      }

      return {
        status: false,
        message: 'Invalid sub_data_source for direct extract validation'
      };

    } catch (error) {
      return {
        status: false,
        message: `Direct extract validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Helper functions for Step 3 validation

  /**
   * Check if configuration exists for the given DCF, entity, and reporting period
   */
  private async checkConfigurationExists(dcfName: string, entity: string, reportingPeriodFrom: string, reportingPeriodTo: string): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Check if DCF configuration exists in FormCollection
      const formConfig = await this.formCollectionRepository.findOne({
        where: {name: dcfName}
      });

      if (!formConfig) {
        return {
          status: false,
          message: `DCF configuration not found for: ${dcfName}`
        };
      }

      // Check if entity has access to this DCF using UserProfileController
      const dcfAssignments = await this.userProfileController.getAssignedIndicatorList(94);

      // Validate entity access and reporting period compatibility
      // This is a simplified check - you may need to add more specific logic
      const hasAccess = dcfAssignments && Array.isArray(dcfAssignments) && dcfAssignments.length > 0;

      if (!hasAccess) {
        return {
          status: false,
          message: `Entity ${entity} does not have access to DCF: ${dcfName}`
        };
      }

      return {
        status: true,
        message: 'Configuration exists and is accessible',
        data: {
          form_config: formConfig,
          entity: entity,
          reporting_period: {from: reportingPeriodFrom, to: reportingPeriodTo},
          frequency: formConfig.frequency || 'Monthly' // Get frequency from config
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Configuration check error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get associated DCF forms for a given indicator using getAssignedIndicator from UserController
   * which returns indicators with dcfIds
   */
  private async getAssociatedDcfForIndicatorUsingController(indicatorName: string): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Find the indicator configuration
      const indicatorConfig = await this.newMetricRepository.findOne({
        where: {name: indicatorName}
      });

      if (!indicatorConfig) {
        return {
          status: false,
          message: `Indicator not found: ${indicatorName}`
        };
      }

      // Use getAssignedIndicator from UserController which returns indicators with dcfIds
      const indicatorData = await this.userProfileController.getAssignedIndicator(94, {
        indicatorId: [indicatorConfig.id!]
      });

      if (!indicatorData || !Array.isArray(indicatorData) || indicatorData.length === 0) {
        return {
          status: false,
          message: `No assignments found for indicator: ${indicatorName}`
        };
      }

      // Extract DCF names from the indicator data (which contains dcfIds)
      const associatedDcfs: string[] = [];

      for (const assignment of indicatorData) {
        // The getAssignedIndicator returns indicators with dcfIds
        if (assignment.dcfIds && Array.isArray(assignment.dcfIds)) {
          for (const dcfId of assignment.dcfIds) {
            // Get DCF name from FormCollection using dcfId
            const dcfConfig = await this.formCollectionRepository.findOne({
              where: {id: dcfId}
            });

            if (dcfConfig && dcfConfig.name && !associatedDcfs.includes(dcfConfig.name)) {
              associatedDcfs.push(dcfConfig.name);
            }
          }
        }

        // Also check for direct dcf_name or form_name properties
        if (assignment.dcf_name || assignment.form_name) {
          const dcfName = assignment.dcf_name || assignment.form_name;
          if (!associatedDcfs.includes(dcfName)) {
            associatedDcfs.push(dcfName);
          }
        }
      }

      if (associatedDcfs.length === 0) {
        return {
          status: false,
          message: `No associated DCF forms found for indicator: ${indicatorName}`
        };
      }

      return {
        status: true,
        message: `Found ${associatedDcfs.length} associated DCF forms for indicator: ${indicatorName}`,
        data: associatedDcfs
      };

    } catch (error) {
      return {
        status: false,
        message: `Error getting associated DCFs: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Dummy resolveReportingFrequency function - will be replaced in next prompt
   */
  private async resolveReportingFrequencyDummy(configData: any): Promise<{status: boolean; message: string; data?: any}> {
    try {
      const frequency_list = [
        {name: 'Monthly', id: 1},
        {name: 'Bi-Monthly', id: 2},
        {name: 'Quarterly', id: 3},
        {name: 'Annually', id: 4},
        {name: 'Bi-Annually', id: 5}
      ];

      // Get frequency from config or default to Monthly
      const configuredFrequency = configData.frequency || 'Monthly';
      const selectedFrequency = frequency_list.find(f => f.name === configuredFrequency) || frequency_list[0];

      return {
        status: true,
        message: `Reporting frequency resolved: ${selectedFrequency.name}`,
        data: {
          frequency: selectedFrequency,
          frequency_id: selectedFrequency.id,
          configured_frequency: configuredFrequency
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Frequency resolution error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
