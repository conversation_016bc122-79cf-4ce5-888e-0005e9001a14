import {inject} from '@loopback/core';
import {post, requestBody, response} from '@loopback/rest';
import {UserProfileController} from './user-profile.controller';

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController
  ) { }

  @post('/stt-report/validate-input-parameters')
  @response(200, {
    description: 'STT Report Input Validation',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            validationType: {type: 'number'}
          }
        }
      }
    },
  })
  async validateInputParameters(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            "main_data_source": {
              type: 'string',
              enum: ['Quantitative', 'Qualitative', 'Config']
            },
            "sub_data_source": {
              type: 'string',
              enum: ['raw', 'indicator']
            },
            "raw_parameters": {
              type: 'object',
              properties: {
                "indicator_name": {type: 'array', items: {type: 'number'}},
                "dcf_name": {type: 'array', items: {type: 'number'}},
                "sub_status": {type: 'string'},
                "breakdown": {type: 'boolean'},
                "breakdown_data": {type: 'string'},


              }
            }
          },
          required: [
            'main_data_source',
            'sub_data_source',
            'raw_parameters'
          ]
        },
      },
    },
  })
  inputParameters: {
    main_data_source: string;
    sub_data_source: string;
    raw_parameters: {
      indicator_name?: number[];
      dcf_name?: number[];
      sub_status?: string;
      breakdown?: boolean;
      breakdown_data?: string;
    };
  }): Promise<{status: boolean; message: string; data?: any}> {

    const {main_data_source, sub_data_source, raw_parameters} = inputParameters;

    // Validate main data source
    const validMainDataSources = ['Quantitative', 'Qualitative', 'Config'];
    const validSubDataSources = ['raw', 'indicator'];

    if (!validMainDataSources.includes(main_data_source)) {
      return {
        status: false,
        message: `Invalid main_data_source. Must be one of: ${validMainDataSources.join(', ')}`
      };
    }

    if (!validSubDataSources.includes(sub_data_source)) {
      return {
        status: false,
        message: `Invalid sub_data_source. Must be one of: ${validSubDataSources.join(', ')}`
      };
    }

    if (!raw_parameters || Object.keys(raw_parameters).length === 0) {
      return {
        status: false,
        message: 'raw_parameters cannot be empty'
      };
    }

    // Determine validation type based on data source combination
    if (main_data_source === 'Quantitative') {
      if (sub_data_source === 'raw') {



        return {
          status: true,
          message: 'Valid parameters for Quantitative raw data'
        };
      } else if (sub_data_source === 'indicator') {
        const indicatorList = await this.userProfileController.getAssignedIndicator(94, {indicatorId: raw_parameters.indicator_name ? raw_parameters.indicator_name : []})
        return {
          status: true,
          message: 'Valid parameters for Quantitative indicator data',
          data: indicatorList
        };
      }
    } else if (main_data_source === 'Qualitative') {
      return {
        status: false,
        message: 'Not developed yet'
      };
    } else if (main_data_source === 'Config') {
      return {
        status: false,
        message: 'Not developed yet'
      };
    }

    return {
      status: false,
      message: 'Invalid combination of parameters'
    };
  }

  @post('/stt-report/process-dcf-data')
  @response(200, {
    description: 'Process DCF Data for STT Report',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {type: 'object'}
          }
        }
      }
    },
  })
  async processDcfData(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            "dcf_ids": {
              type: 'array',
              items: {type: 'number'}
            },
            "status": {
              type: 'string'
            },
            "breakdown": {
              type: 'string'
            },
            "breakdown_name": {
              type: 'string'
            }
          },
          required: [
            'dcf_ids',
            'status',
            'breakdown',
            'breakdown_name'
          ]
        },
      },
    },
  })
  requestData: {
    dcf_ids: number[];
    status: string;
    breakdown: string;
    breakdown_name: string;
  }): Promise<{status: boolean; message: string; data?: any}> {

    const {dcf_ids, status, breakdown, breakdown_name} = requestData;

    try {
      // Validate input parameters
      if (!dcf_ids || !Array.isArray(dcf_ids) || dcf_ids.length === 0) {
        return {
          status: false,
          message: 'dcf_ids must be a non-empty array of numbers'
        };
      }

      if (!status || typeof status !== 'string') {
        return {
          status: false,
          message: 'status must be a non-empty string'
        };
      }

      if (!breakdown || typeof breakdown !== 'string') {
        return {
          status: false,
          message: 'breakdown must be a non-empty string'
        };
      }

      if (!breakdown_name || typeof breakdown_name !== 'string') {
        return {
          status: false,
          message: 'breakdown_name must be a non-empty string'
        };
      }

      // Validate that all dcf_ids are numbers
      const invalidIds = dcf_ids.filter(id => typeof id !== 'number' || isNaN(id));
      if (invalidIds.length > 0) {
        return {
          status: false,
          message: `Invalid dcf_ids found: ${invalidIds.join(', ')}. All IDs must be valid numbers.`
        };
      }

      // Process the data based on the parameters
      const processedData = {
        dcf_ids: dcf_ids,
        status: status,
        breakdown: breakdown,
        breakdown_name: breakdown_name,
        processed_at: new Date().toISOString(),
        total_records: dcf_ids.length
      };

      // TODO: Add your business logic here
      // For example:
      // - Fetch DCF data based on dcf_ids
      // - Filter by status
      // - Apply breakdown logic
      // - Generate report data

      return {
        status: true,
        message: `Successfully processed ${dcf_ids.length} DCF records with status '${status}' and breakdown '${breakdown_name}'`,
        data: processedData
      };

    } catch (error) {
      return {
        status: false,
        message: `Error processing DCF data: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
