import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, requestBody, response} from '@loopback/rest';
import {
  FormCollectionRepository,
  NewMetricRepository,
  UserProfileRepository
} from '../repositories';
import {UserProfileController} from './user-profile.controller';

interface DcfAssignment {
  dcfId: string;
  locationId: number;
  level: number;
  start_date: string;
  end_date: string | null;
  frequency: number;
  reportingPeriods?: ReportingPeriod;
}

interface ReportingPeriod {
  valid_periods: string[];
  data_granularity: string;
}

interface ReportingFrequencyInput {
  reporting_frequency: string;
  reporting_period_from?: string;
  reporting_period_to?: string;
  reporting_period: string;
  year: string;
}

const frequencyMapping: {[key: number]: string} = {
  1: 'monthly',
  2: 'bi-monthly',
  3: 'quarterly',
  4: 'yearly',
  5: 'half-yearly'
};

interface FrequencyWeight {
  [key: string]: number;
  monthly: number;
  'bi-monthly': number;
  quarterly: number;
  'half-yearly': number;
  yearly: number;
  custom: number;
}

export class SttReportController {
  constructor(
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository
  ) { }

  // Add frequency weights as a class property
  private frequencyWeights: FrequencyWeight = {
    monthly: 1,
    'bi-monthly': 2,
    quarterly: 3,
    'half-yearly': 4,
    yearly: 5,
    custom: 1
  };

  private getFrequencyFromCode(code: number): string {
    switch (code) {
      case 1: return 'monthly';
      case 2: return 'bi-monthly';
      case 3: return 'quarterly';
      case 4: return 'yearly';
      case 5: return 'half-yearly';
      case 6: return 'custom';
      default: throw new Error(`Invalid frequency code: ${code}`);
    }
  }

  private validateFrequencyCompatibility(
    requestedFrequency: string,
    assignment: any,
    customPeriodFrom?: string,
    customPeriodTo?: string,
    year?: string
  ): {isValid: boolean; incompatibleReason?: string} {
    // First validate that the requested period falls within the assignment period
    const assignmentStart = new Date(assignment.start_date);
    const assignmentEnd = assignment.end_date ? new Date(assignment.end_date) : new Date();

    // Normalize the requested frequency
    const normalizedFrequency = requestedFrequency.toLowerCase();

    // Different date range validation for custom vs non-custom periods
    if (normalizedFrequency === 'custom') {
      // For custom periods, validate using the exact from/to dates
      if (!customPeriodFrom || !customPeriodTo) {
        return {
          isValid: false,
          incompatibleReason: 'Custom period requires both start and end dates'
        };
      }

      const requestStart = new Date(customPeriodFrom);
      const requestEnd = new Date(customPeriodTo);

      // Set times to start and end of day for accurate comparison
      requestStart.setUTCHours(0, 0, 0, 0);
      requestEnd.setUTCHours(23, 59, 59, 999);
      assignmentStart.setUTCHours(0, 0, 0, 0);
      assignmentEnd.setUTCHours(23, 59, 59, 999);

      if (requestStart < assignmentStart || requestEnd > assignmentEnd) {
        return {
          isValid: false,
          incompatibleReason: `Requested period (${customPeriodFrom} to ${customPeriodTo}) must fall within the assignment period (${assignment.start_date.split('T')[0]} to ${assignment.end_date ? assignment.end_date.split('T')[0] : 'present'})`
        };
      }
    } else {
      // For non-custom frequencies, validate using the year
      if (!year) {
        return {
          isValid: false,
          incompatibleReason: 'Year is required for non-custom periods'
        };
      }

      const requestedYear = parseInt(year);
      const assignmentStartYear = assignmentStart.getUTCFullYear();
      const assignmentEndYear = assignmentEnd.getUTCFullYear();

      if (requestedYear < assignmentStartYear || requestedYear > assignmentEndYear) {
        return {
          isValid: false,
          incompatibleReason: `Requested year ${requestedYear} must fall within the assignment period (${assignmentStartYear} to ${assignmentEndYear})`
        };
      }
    }

    // Get assignment frequency details
    const assignmentFreq = this.getFrequencyFromCode(assignment.frequency);
    const assignmentWeight = this.frequencyWeights[assignmentFreq];

    // If custom period, we need to validate period alignment
    if (normalizedFrequency === 'custom') {
      // We already validated that customPeriodFrom and customPeriodTo exist above
      if (!customPeriodFrom || !customPeriodTo) {
        return {
          isValid: false,
          incompatibleReason: 'Custom period requires both start and end dates'
        };
      }

      // For custom periods, check if the assignment frequency can support the granularity
      if (assignmentWeight > 1) {
        // For frequencies other than monthly, validate period alignment
        const startMonth = parseInt(customPeriodFrom.split('-')[1]);
        const endMonth = parseInt(customPeriodTo.split('-')[1]);

        switch (assignmentWeight) {
          case 2: // bi-monthly
            // Start month must be odd (1,3,5,7,9,11)
            // End month must be even (2,4,6,8,10,12)
            const isStartValid = startMonth % 2 === 1;
            const isEndValid = endMonth % 2 === 0;
            const isComplete = (endMonth - startMonth + 1) % 2 === 0;

            if (!isStartValid || !isEndValid || !isComplete) {
              return {
                isValid: false,
                incompatibleReason: `Custom period must align with bi-monthly periods (Jan-Feb, Mar-Apr, etc.). ` +
                  `Current period (Month ${startMonth} to Month ${endMonth}) is invalid.`
              };
            }
            break;

          case 3: // quarterly
            // Start month must be first of quarter (1,4,7,10)
            // End month must be last of quarter (3,6,9,12)
            const isStartQuarter = startMonth % 3 === 1;
            const isEndQuarter = endMonth % 3 === 0;
            const isCompleteQuarter = (endMonth - startMonth + 1) % 3 === 0;

            if (!isStartQuarter || !isEndQuarter || !isCompleteQuarter) {
              return {
                isValid: false,
                incompatibleReason: `Custom period must align with quarterly periods (Jan-Mar, Apr-Jun, etc.). ` +
                  `Current period (Month ${startMonth} to Month ${endMonth}) is invalid.`
              };
            }
            break;

          case 4: // half-yearly
            // Start month must be first of half (1,7)
            // End month must be last of half (6,12)
            const isStartHalf = startMonth === 1 || startMonth === 7;
            const isEndHalf = endMonth === 6 || endMonth === 12;
            const isCompleteHalf = (endMonth - startMonth + 1) % 6 === 0;

            if (!isStartHalf || !isEndHalf || !isCompleteHalf) {
              return {
                isValid: false,
                incompatibleReason: `Custom period must align with half-yearly periods (Jan-Jun, Jul-Dec). ` +
                  `Current period (Month ${startMonth} to Month ${endMonth}) is invalid.`
              };
            }
            break;

          case 5: // yearly
            // Must be full year
            if (startMonth !== 1 || endMonth !== 12) {
              return {
                isValid: false,
                incompatibleReason: `Custom period must cover full year (Jan-Dec) for yearly frequency. ` +
                  `Current period (Month ${startMonth} to Month ${endMonth}) is invalid.`
              };
            }
            break;
        }
      }

      return {
        isValid: true
      };
    }

    // For non-custom frequencies
    const requestedWeight = this.frequencyWeights[normalizedFrequency];
    if (!requestedWeight) {
      throw new Error(`Invalid requested frequency: ${requestedFrequency}`);
    }

    // Assignment frequency should be equal to or higher (weight less than or equal) than requested
    const isValid = assignmentWeight <= requestedWeight;

    console.log('Frequency Validation:', {
      requested: {
        frequency: requestedFrequency,
        weight: requestedWeight
      },
      assignment: {
        frequency: assignmentFreq,
        weight: assignmentWeight
      },
      isValid
    });

    return {
      isValid,
      incompatibleReason: isValid ? undefined :
        `Assignment frequency (${assignmentFreq}) cannot support requested frequency (${requestedFrequency}). Required frequency should be ${requestedFrequency} or higher.`
    };
  }

  @post('/stt-report/generate-report')
  @response(200, {
    description: 'STT Report Generation - Main entry function with complete input format',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {type: 'object'}
          }
        }
      }
    },
  })
  async generateReport(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {
          type: 'object',
          properties: {
            main_data_source: {
              type: 'string',
              enum: ['quantitative', 'qualitative', 'config'],
              description: 'Main data source type'
            },
            sub_data_source: {
              type: 'string',
              enum: ['raw', 'indicator'],
              description: 'Required if main_data_source is Quantitative'
            },
            raw_parameters: {
              type: 'object',
              description: 'Required if sub_data_source is raw',
              properties: {
                dcf_name: {
                  type: 'array',
                  items: {type: 'number'},
                  description: 'Single DCF name in array format'
                },
                sub_status: {
                  type: 'string',
                  enum: ['live', 'locked'],
                  description: 'live - other than draft, locked - only approved'
                },
                breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable breakdown'
                },
                breakdown_data: {
                  type: 'string',
                  enum: ['dcf_name', 'data_point_name'],
                  description: 'Required if breakdown is true'
                }
              }
            },
            indicator_parameters: {
              type: 'object',
              description: 'Required if sub_data_source is indicator',
              properties: {
                indicator_name: {
                  type: 'array',
                  items: {type: 'string'},
                  description: 'Single indicator name in array format'
                },
                sub_status: {
                  type: 'string',
                  enum: ['locked', 'breakdown'],
                  description: 'Status for indicator data'
                },
                breakdown_data: {
                  type: 'array',
                  items: {type: 'number'},
                  description: 'Required if sub_status is breakdown'
                }
              }
            },
            filter_type: {
              type: 'string',
              enum: ['applied_common_filter', 'applied_specific_filter'],
              description: 'Type of filter to apply'
            },
            applied_common_filter: {
              type: 'object',
              description: 'Filters applied on the template by the user',
              properties: {
                year: {
                  type: 'number',
                  description: 'Year filter'
                },
                reporting_period: {
                  type: 'string',
                  enum: ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'],
                  description: 'Reporting period type'
                },
                reporting_period_from: {
                  type: 'string',
                  description: 'Start period (e.g., 2023-01)'
                },
                reporting_period_to: {
                  type: 'string',
                  description: 'End period (e.g., 2023-12)'
                },
                locked_date: {
                  type: 'string',
                  description: 'Locked date (e.g., 2024-01-15) or NA'
                },
                entity: {
                  type: 'array',
                  items: {type: 'string'},
                  description: 'Entity ID or Name'
                }
              }
            },
            applied_specific_filter: {
              type: 'object',
              description: 'Filters applied through the code',
              properties: {
                year: {
                  type: 'number',
                  description: 'Year filter'
                },
                reporting_period: {
                  type: 'string',
                  enum: ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'],
                  description: 'Reporting period type'
                },
                reporting_period_from: {
                  type: 'string',
                  description: 'Start period (e.g., 2023-01)'
                },
                reporting_period_to: {
                  type: 'string',
                  description: 'End period (e.g., 2023-12)'
                },
                locked_date: {
                  type: 'string',
                  description: 'Locked date (e.g., 2024-01-15) or NA'
                },
                entity: {
                  type: 'array',
                  items: {type: 'string'},
                  description: 'Entity ID or Name'
                }
              }
            },
            type_of_data: {
              type: 'string',
              enum: ['queried_data', 'direct_extract'],
              description: 'Data extraction type'
            },
            type_of_format: {
              type: 'string',
              enum: ['tabular_form_data', 'value_field', 'chart_data'],
              description: 'Output format type'
            },
            query_details: {
              type: 'object',
              description: 'Required only if type_of_data is queried_data',
              properties: {
                query_type: {
                  type: 'string',
                  enum: ['sum', 'ratio', 'percentage', 'count'],
                  description: 'Type of query operation'
                },
                sub_query_type: {
                  type: 'string',
                  description: 'Sub query type (e.g., sum for entity)'
                },
                query_parameters: {
                  type: 'object',
                  description: 'Query parameters: filter_type, entity selected, reporting period'
                }
              }
            },
            table_config: {
              type: 'object',
              description: 'Required only if type_of_format is tabular_form_data',
              properties: {
                period_breakdown: {
                  type: 'string',
                  description: 'Options: monthly, quarterly'
                },
                entity_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable entity breakdown'
                },
                entity_details: {
                  type: 'string',
                  description: 'Entity selected - required if entity_breakdown is true'
                },
                dcf_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable DCF breakdown'
                }
              }
            },
            chart_config: {
              type: 'object',
              description: 'Required only if type_of_format is chart_data',
              properties: {
                period_breakdown: {
                  type: 'string',
                  description: 'Options: monthly, quarterly'
                },
                entity_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable entity breakdown'
                },
                dcf_breakdown: {
                  type: 'boolean',
                  description: 'Enable/disable DCF breakdown'
                },
                entity_details: {
                  type: 'string',
                  description: 'Entity selected - required if entity_breakdown is true'
                }
              }
            }
          },
          required: ['main_data_source', 'sub_data_source', 'filter_type']
        }
      }
    }
  })
  requestData: any): Promise<{status: boolean; message: string; data?: any}> {

    try {
      // Destructure the complete input parameters
      const {
        main_data_source,
        sub_data_source,
        raw_parameters,
        indicator_parameters,
        filter_type,
        applied_common_filter,
        applied_specific_filter,
        type_of_data,
        type_of_format,
        query_details,
        table_config,
        chart_config
      } = requestData;
      const mainDataSourceType = ['quantitative', 'qualitative', 'config'];
      const subDataSourceType = ['raw', 'indicator'];
      const frequencyType = ['Monthly', 'Bi-Monthly', 'Quarterly', 'Half-Yearly', 'Yearly', 'Custom'];
      const activeFilterType = filter_type === 'applied_common_filter' ? applied_common_filter : filter_type === 'applied_specific_filter' ? applied_specific_filter : null
      if (!activeFilterType) {
        return {
          status: false,
          message: 'Missing filter type'
        };
      }

      // Function to validate input parameters
      const validateInputParameters = async (main_data_source: any, sub_data_source: any, indicator_parameters: any, raw_parameters: any, activeFilterType: any) => {
        // Validate year limit first, before any other processing
        const maxAllowedYear = 2025;
        const requestedYear = activeFilterType.reporting_period === 'Custom'
          ? new Date(activeFilterType.reporting_period_from).getFullYear()
          : parseInt(activeFilterType.year);

        if (requestedYear > maxAllowedYear) {
          return {
            status: false,
            message: `[VALIDATION ERROR] Cannot request assignments for year ${requestedYear}. Maximum allowed year is ${maxAllowedYear}.`
          };
        }

        if (activeFilterType.reporting_period === 'Custom') {
          const endYear = new Date(activeFilterType.reporting_period_to).getFullYear();
          if (endYear > maxAllowedYear) {
            return {
              status: false,
              message: `[VALIDATION ERROR] Custom period end year ${endYear} exceeds maximum allowed year ${maxAllowedYear}.`
            };
          }
        }

        // Check if main_data_source is valid
        if (!mainDataSourceType.includes(main_data_source)) {
          return {
            status: false,
            message: '[VALIDATION ERROR] Invalid main_data_source. Expected one of: ' + mainDataSourceType.join(', ')
          };
        }
        if (!subDataSourceType.includes(sub_data_source)) {
          return {
            status: false,
            message: '[VALIDATION ERROR] Invalid sub_data_source. Expected one of: ' + subDataSourceType.join(', ')
          };
        }
        if (!activeFilterType.entity || !Array.isArray(activeFilterType.entity) || activeFilterType.entity.length === 0 || !activeFilterType.reporting_period || !frequencyType.includes(activeFilterType.reporting_period) || !activeFilterType.year || (activeFilterType.reporting_period === 'Custom' && (!activeFilterType.reporting_period_from || !activeFilterType.reporting_period_to))) {
          let errorDetails = [];
          if (!activeFilterType.reporting_period) errorDetails.push('reporting_period is required');
          if (!frequencyType.includes(activeFilterType.reporting_period)) errorDetails.push(`reporting_period must be one of: ${frequencyType.join(', ')}`);
          if (!activeFilterType.year) errorDetails.push('year is required');
          if (activeFilterType.reporting_period === 'Custom' && (!activeFilterType.reporting_period_from || !activeFilterType.reporting_period_to)) {
            errorDetails.push('reporting_period_from and reporting_period_to are required for Custom period');
          }
          if (!activeFilterType.entity || !Array.isArray(activeFilterType.entity) || activeFilterType.entity.length === 0) {
            errorDetails.push('entity is required and must be an array');
          }

          return {
            status: false,
            message: '[VALIDATION ERROR] Invalid filter parameters:\n' + errorDetails.join('\n')
          };
        }
        if (main_data_source === 'quantitative') {
          if (sub_data_source === 'raw') {
            const dcfAssignment = await this.userProfileController.getAssignedIndicatorList(94, {
              where: {
                dcfId: {inq: raw_parameters?.dcf_name || []},
                locationId: {inq: activeFilterType.entity.map((entity: any) => entity.split('-')[1]).map((id: any) => parseInt(id)) || []}
              }
            }) as DcfAssignment[];
            console.log(raw_parameters?.dcf_name, activeFilterType.entity)
            if (!dcfAssignment || dcfAssignment.length === 0) {
              return {
                status: false,
                message: '[VALIDATION ERROR] No valid DCF assignments found for the given input'
              };
            }

            // Calculate period based on reporting_period type
            let periodStart: Date;
            let periodEnd: Date;
            let formattedPeriodStart: string;
            let formattedPeriodEnd: string;

            if (activeFilterType.reporting_period === 'Custom') {
              periodStart = new Date(activeFilterType.reporting_period_from);
              periodEnd = new Date(activeFilterType.reporting_period_to);
            } else {
              // For non-custom periods, use Jan to Dec of the selected year
              periodStart = new Date(activeFilterType.year + '-01-01');
              periodEnd = new Date(activeFilterType.year + '-12-31');
            }

            // Format periods as MMM-YYYY in UTC
            formattedPeriodStart = periodStart.toLocaleString('en-US', {month: 'short', year: 'numeric', timeZone: 'UTC'});
            formattedPeriodEnd = periodEnd.toLocaleString('en-US', {month: 'short', year: 'numeric', timeZone: 'UTC'});

            // First filter assignments based on period with UTC dates
            const validPeriodAssignments = dcfAssignment.filter((assignment: DcfAssignment) => {
              // Convert period dates to UTC midnight for comparison
              const periodStartUTC = new Date(periodStart.getTime());
              periodStartUTC.setUTCHours(0, 0, 0, 0);
              const periodEndUTC = new Date(periodEnd.getTime());
              periodEndUTC.setUTCHours(23, 59, 59, 999);

              // Parse assignment dates (they are already in UTC from database)
              const assignmentStart = new Date(assignment.start_date);
              const assignmentEnd = assignment.end_date
                ? new Date(assignment.end_date)
                : new Date(); // Current date if end_date is null

              console.log(`Checking assignment for dcfId: ${assignment.dcfId}, locationId: ${assignment.locationId}`);
              console.log(`Period: ${periodStartUTC.toISOString()} to ${periodEndUTC.toISOString()}`);
              console.log(`Assignment: ${assignmentStart.toISOString()} to ${assignmentEnd.toISOString()}`);

              // Compare dates - an assignment is valid if:
              // 1. Assignment start is before or equal to period end AND
              // 2. Assignment end is after or equal to period start
              const isValid = assignmentStart <= periodEndUTC && assignmentEnd >= periodStartUTC;
              console.log(`Is valid: ${isValid}`);

              return isValid;
            });

            if (!validPeriodAssignments || validPeriodAssignments.length === 0) {
              return {
                status: false,
                message: `[VALIDATION ERROR] No assignments found for the specified period (${formattedPeriodStart} to ${formattedPeriodEnd})`
              };
            }

            console.log(`Found ${validPeriodAssignments.length} valid period assignments`);

            // Create a map to track assignments for each DCF-entity combination
            const assignmentMap = new Map<string, DcfAssignment>();
            const missingCombinations: string[] = [];
            const invalidPeriodAssignments: string[] = [];

            // Check each required combination
            for (const dcfId of (raw_parameters?.dcf_name || [])) {
              for (const entity of (activeFilterType.entity || [])) {
                const key = `${dcfId}-${entity}`;

                // Find assignment for this combination from period-filtered assignments
                const assignment = validPeriodAssignments.find((a: DcfAssignment) =>
                  key === a.dcfId + '-' + a.level + '-' + a.locationId
                );

                if (!assignment) {
                  missingCombinations.push(`DCF: ${dcfId}, Entity: ${entity}`);
                  continue;
                }

                // Check frequency compatibility
                const frequencyResult = this.resolveReportingFrequency(
                  {
                    reporting_frequency: frequencyMapping[assignment?.frequency],
                    reporting_period_from: activeFilterType.reporting_period_from,
                    reporting_period: activeFilterType.reporting_period,
                    reporting_period_to: activeFilterType.reporting_period_to,
                    year: activeFilterType.year
                  },
                  assignment
                );

                if (!frequencyResult.isValid) {
                  return {
                    status: false,
                    message: `[VALIDATION ERROR] Invalid frequency for DCF: ${dcfId}, Entity: ${entity}. ${frequencyResult.error}`
                  };
                }

                assignmentMap.set(key, {...assignment, reportingPeriods: frequencyResult.periods});
              }
            }

            // If we have any missing combinations, return error
            if (missingCombinations.length > 0) {
              return {
                status: false,
                message: `[VALIDATION ERROR] Missing assignments for the following combinations in period ${formattedPeriodStart} to ${formattedPeriodEnd}:\n${missingCombinations.join('\n')}`
              };
            }

            // All validations passed
            return {
              status: true,
              message: `All ${raw_parameters?.dcf_name?.length * activeFilterType.entity?.length} DCF-entity combinations have valid assignments for the period ${formattedPeriodStart} to ${formattedPeriodEnd}`,
              data: {
                validAssignments: Array.from(assignmentMap.values()),
                period: {
                  start: formattedPeriodStart,
                  end: formattedPeriodEnd,
                  valid_periods: assignmentMap.values().next().value?.reportingPeriods?.valid_periods || [],
                  data_granularity: assignmentMap.values().next().value?.reportingPeriods?.data_granularity
                },
                coverage: {
                  totalCombinations: raw_parameters?.dcf_name?.length * activeFilterType.entity?.length,
                  validAssignments: assignmentMap.size
                }
              }
            };
          } else if (sub_data_source === 'indicator') {
            const indicatorList = (await this.userProfileController.getAssignedIndicator(94, {indicatorId: indicator_parameters?.indicator_name || []})).flatMap((x: any) => x.dcfIds || [])
            if (indicatorList.length) {
              validateInputParameters('quantitative', 'raw', null, {dcf_name: Array.from(new Set(indicatorList)), sub_status: indicator_parameters?.sub_status, breakdown: indicator_parameters?.breakdown, breakdown_data: indicator_parameters.breakdown_data}, activeFilterType)
            } else {
              return {
                status: false,
                message: '[VALIDATION ERROR] No indicators found. Please check the indicator parameters provided'
              };
            }
          }
        }
        return {status: true, message: 'Validation successful'};
      };

      // Call the validation function
      const validationResult = await validateInputParameters(main_data_source, sub_data_source, indicator_parameters, raw_parameters, activeFilterType) || {status: false, message: 'Invalid input parameters'}
      if (!validationResult.status) {
        return validationResult;
      }

      // TODO: Implement the main report generation logic here
      // This is where you would call other functions with the destructured parameters

      return {
        status: true,
        message: 'Report generation completed successfully with validation',
        data: {
          validation_result: validationResult,
          received_parameters: {
            main_data_source,
            sub_data_source,
            raw_parameters,
            indicator_parameters,
            filter_type,
            applied_common_filter,
            applied_specific_filter,
            type_of_data,
            type_of_format,
            query_details,
            table_config,
            chart_config
          }
        }
      };

    } catch (error) {
      return {
        status: false,
        message: `Error in report generation: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private resolveReportingFrequency(
    input: ReportingFrequencyInput,
    assignment: any
  ): {isValid: boolean; periods?: ReportingPeriod; error?: string} {
    try {
      // First validate frequency compatibility
      const frequencyValidation = this.validateFrequencyCompatibility(
        input.reporting_period,
        assignment,
        input.reporting_period_from,
        input.reporting_period_to,
        input.year
      );

      if (!frequencyValidation.isValid) {
        return {
          isValid: false,
          error: frequencyValidation.incompatibleReason
        };
      }

      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      let periods: ReportingPeriod;

      // Handle custom period
      if (input.reporting_period.toLowerCase() === 'custom') {
        if (!input.reporting_period_from || !input.reporting_period_to) {
          return {
            isValid: false,
            error: 'Custom period requires both start and end dates'
          };
        }

        const startDate = new Date(input.reporting_period_from);
        const endDate = new Date(input.reporting_period_to);
        const validPeriods: string[] = [];

        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth();
          validPeriods.push(`${year}-${(month + 1).toString().padStart(2, '0')}`);
          currentDate.setMonth(currentDate.getMonth() + 1);
        }

        periods = {
          valid_periods: validPeriods,
          data_granularity: input.reporting_frequency
        };
      } else {
        // Handle standard periods
        const year = input.year;
        // Use the requested reporting period instead of assignment frequency
        const requestedPeriod = input.reporting_period.toLowerCase();

        switch (requestedPeriod) {
          case 'monthly':
            periods = {
              valid_periods: months.map((month, index) =>
                `${year}-${(index + 1).toString().padStart(2, '0')}`
              ),
              data_granularity: 'monthly'
            };
            break;

          case 'bi-monthly':
            periods = {
              valid_periods: Array.from({length: 6}, (_, i) =>
                `BM${i + 1}-${year}`
              ),
              data_granularity: 'bi-monthly'
            };
            break;

          case 'quarterly':
            periods = {
              valid_periods: ['Q1', 'Q2', 'Q3', 'Q4'].map(q => `${q}-${year}`),
              data_granularity: 'quarterly'
            };
            break;

          case 'half-yearly':
            periods = {
              valid_periods: ['H1', 'H2'].map(h => `${h}-${year}`),
              data_granularity: 'half-yearly'
            };
            break;

          case 'yearly':
            periods = {
              valid_periods: [year.toString()],
              data_granularity: 'yearly'
            };
            break;

          default:
            return {
              isValid: false,
              error: `Unsupported reporting period: ${input.reporting_period}`
            };
        }
      }

      return {
        isValid: true,
        periods
      };

    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error in resolving reporting frequency'
      };
    }
  }

}
