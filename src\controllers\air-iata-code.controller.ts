import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AirIataCode} from '../models';
import {AirIataCodeRepository} from '../repositories';

export class AirIataCodeController {
  constructor(
    @repository(AirIataCodeRepository)
    public airIataCodeRepository : AirIataCodeRepository,
  ) {}

  @post('/air-iata-codes')
  @response(200, {
    description: 'AirIataCode model instance',
    content: {'application/json': {schema: getModelSchemaRef(AirIataCode)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {
            title: 'NewAirIataCode',
            exclude: ['id'],
          }),
        },
      },
    })
    airIataCode: Omit<AirIataCode, 'id'>,
  ): Promise<AirIataCode> {
    return this.airIataCodeRepository.create(airIataCode);
  }

  @get('/air-iata-codes/count')
  @response(200, {
    description: 'AirIataCode model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<Count> {
    return this.airIataCodeRepository.count(where);
  }

  @get('/air-iata-codes')
  @response(200, {
    description: 'Array of AirIataCode model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirIataCode, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AirIataCode) filter?: Filter<AirIataCode>,
  ): Promise<AirIataCode[]> {
    return this.airIataCodeRepository.find(filter);
  }

  @patch('/air-iata-codes')
  @response(200, {
    description: 'AirIataCode PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {partial: true}),
        },
      },
    })
    airIataCode: AirIataCode,
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<Count> {
    return this.airIataCodeRepository.updateAll(airIataCode, where);
  }

  @get('/air-iata-codes/{id}')
  @response(200, {
    description: 'AirIataCode model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AirIataCode, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AirIataCode, {exclude: 'where'}) filter?: FilterExcludingWhere<AirIataCode>
  ): Promise<AirIataCode> {
    return this.airIataCodeRepository.findById(id, filter);
  }

  @patch('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {partial: true}),
        },
      },
    })
    airIataCode: AirIataCode,
  ): Promise<void> {
    await this.airIataCodeRepository.updateById(id, airIataCode);
  }

  @put('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() airIataCode: AirIataCode,
  ): Promise<void> {
    await this.airIataCodeRepository.replaceById(id, airIataCode);
  }

  @del('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.airIataCodeRepository.deleteById(id);
  }
}
