import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {DealerChecklistSubmission} from '../models';
import {DealerChecklistSubmissionRepository, UserProfileRepository, VendorCodeRepository} from '../repositories';

export class DealerChecklistSubmissionController {
  constructor(
    @repository(DealerChecklistSubmissionRepository)
    public dealerChecklistSubmissionRepository: DealerChecklistSubmissionRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
  ) { }

  @post('/dealer-checklist-submissions-not-allowed', {
    responses: {
      '200': {
        description: 'DealerChecklistSubmission model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerChecklistSubmission)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerChecklistSubmission, {
            title: 'NewDealerChecklistSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    dealerChecklistSubmission: Omit<DealerChecklistSubmission, 'id'>,
  ): Promise<DealerChecklistSubmission> {
    return this.dealerChecklistSubmissionRepository.create(dealerChecklistSubmission);
  }

  @get('/dealer-checklist-submissions/count', {
    responses: {
      '200': {
        description: 'DealerChecklistSubmission model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(DealerChecklistSubmission) where?: Where<DealerChecklistSubmission>,
  ): Promise<Count> {
    return this.dealerChecklistSubmissionRepository.count(where);
  }

  @get('/dealer-checklist-submissions', {
    responses: {
      '200': {
        description: 'Array of DealerChecklistSubmission model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DealerChecklistSubmission, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(DealerChecklistSubmission) filter?: Filter<DealerChecklistSubmission>,
  ): Promise<DealerChecklistSubmission[]> {
    return this.dealerChecklistSubmissionRepository.find(filter);
  }

  @patch('/dealer-checklist-submissions', {
    responses: {
      '200': {
        description: 'DealerChecklistSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerChecklistSubmission, {partial: true}),
        },
      },
    })
    dealerChecklistSubmission: DealerChecklistSubmission,
    @param.where(DealerChecklistSubmission) where?: Where<DealerChecklistSubmission>,
  ): Promise<Count> {
    return this.dealerChecklistSubmissionRepository.updateAll(dealerChecklistSubmission, where);
  }

  @get('/dealer-checklist-submissions/{id}', {
    responses: {
      '200': {
        description: 'DealerChecklistSubmission model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerChecklistSubmission, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DealerChecklistSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerChecklistSubmission>
  ): Promise<DealerChecklistSubmission> {
    return this.dealerChecklistSubmissionRepository.findById(id, filter);
  }

  @patch('/dealer-checklist-submissions/{id}', {
    responses: {
      '204': {
        description: 'DealerChecklistSubmission PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerChecklistSubmission, {partial: true}),
        },
      },
    })
    dealerChecklistSubmission: DealerChecklistSubmission,
  ): Promise<void> {
    await this.dealerChecklistSubmissionRepository.updateById(id, dealerChecklistSubmission);
  }

  @put('/dealer-checklist-submissions/{id}', {
    responses: {
      '204': {
        description: 'DealerChecklistSubmission PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dealerChecklistSubmission: DealerChecklistSubmission,
  ): Promise<void> {
    await this.dealerChecklistSubmissionRepository.replaceById(id, dealerChecklistSubmission);
  }

  @del('/dealer-checklist-submissions/{id}', {
    responses: {
      '204': {
        description: 'DealerChecklistSubmission DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dealerChecklistSubmissionRepository.deleteById(id);
  }
  @post('/user-profiles/{id}/dealer-checklist-submissions-status')
  async getSelfAssessmentByDealerId(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            dealerId: {
              type: 'number'
            },
            reporting_period: {type: 'array', items: {type: 'string'}},
          },
          required: ['dealerId', 'reporting_period'],
        },
      },
    },
  })
  requestBody: {dealerId: number, reporting_period: string[]},
    @param.path.number('id') id: number,
  ): Promise<any> {
    const {reporting_period, dealerId} = requestBody
    try {
      const found = await this.userProfileRepository.findById(dealerId)
      if (found && found.clientId === id && found?.information?.assessmentStartMonth) {
        const assignedForm = 33
        const submissionFound = await this.userProfileRepository.dealerChecklistSubmissions(id).find({
          where: {
            dealerId, formId: assignedForm
          }
        })

        if (submissionFound.length && submissionFound.length === 1 && reporting_period.every((item: string) => submissionFound[0].reporting_period?.includes(item))) {
          return {status: submissionFound[0].type === 1 ? 3 : 2, data: submissionFound[0], message: submissionFound[0].type === 1 ? "Self Assessment Submitted" : "Self Assessment Draft"}
        } else {
          return {status: 1, data: [{reporting_period, formId: assignedForm}], message: "Self Assessment Not Submitted"}
        }
      } else if (found && found.clientId === id) {
        return {status: 0, data: [], message: "Self Assessment not started"}
      } else if (found && found.clientId !== id) {
        return {status: 0, data: [], message: "Invalid Enterprise User Login"}
      } else {
        return {status: 0, data: [], message: "Invalid Login"}

      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }
  @post('/user-profiles/{id}/dealer-checklist-submissions-status-custom')
  async getSelfAssessmentByDealerVendorId(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            dealerId: {
              type: 'number'
            },
            vendorId: {
              type: 'number'
            },
            reporting_period: {type: 'array', items: {type: 'string'}},
          },
          required: ['dealerId', 'vendorId', 'reporting_period'],
        },
      },
    },
  })
  requestBody: {dealerId: number, vendorId: number, reporting_period: string[]},
    @param.path.number('id') id: number,
  ): Promise<any> {
    const {reporting_period, dealerId, vendorId} = requestBody
    try {
      const found = await this.userProfileRepository.findById(dealerId)
      if (found && found.clientId === id) {


        const vendor = await this.vendorCodeRepository.findById(vendorId)

        const assignedForm = vendor.dealerCategory === 1 ? 33 : vendor.dealerCategory === 3 ? 7 : vendor.dealerCategory === 4 ? 35 : 33
        if (vendor && vendor.assessmentStartMonth && assignedForm) {
          if (Math.round(DateTime.fromFormat(reporting_period[0], 'MM-yyyy').diff(DateTime.fromISO(vendor.assessmentStartMonth, {zone: "Asia/Calcutta"}).startOf('month'), 'months').months) >= 0) {

            const submissionFound = await this.userProfileRepository.dealerChecklistSubmissions(id).find({
              where: {
                vendorId,
                dealerId, formId: assignedForm
              }
            })

            if (submissionFound.length && submissionFound.length === 1 && reporting_period.every((item: string) => submissionFound[0].reporting_period?.includes(item))) {
              return {status: submissionFound[0].type === 1 ? 3 : 2, data: submissionFound[0], message: submissionFound[0].type === 1 ? "Self Assessment Submitted" : "Self Assessment Draft"}
            } else {
              return {status: 1, data: [{reporting_period, formId: assignedForm}], message: "Self Assessment Not Submitted"}
            }
          } else {
            return {status: 0, data: [], message: "Self Assessment triggers from " + DateTime.fromISO(vendor.assessmentStartMonth, {zone: "Asia/Calcutta"}).startOf('month').toFormat('LLL-yyyy')}

          }
        } else if (vendor) {
          return {status: 0, data: [], message: (!assignedForm && !vendor.assessmentStartMonth) ? "Checklist & Self Assessment not mapped" : vendor.assessmentStartMonth ? "Checklist not mapped" : "Self Assessment not triggered"}

        } else {
          return {status: 0, data: [], message: "Invalid Vendor Login"}

        }

      } else if (found && found.clientId !== id) {
        return {status: 0, data: [], message: "Invalid Enterprise User Login"}
      } else {
        return {status: 0, data: [], message: "Invalid Login"}

      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }
  @post('/user-profiles/{id}/dealer-checklist-rescent-submissions-custom')
  async getDealerRecentSubmissionByDealerId(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            dealerId: {
              type: 'number'
            },
            formId: {
              type: 'number'
            }
          },
          required: ['dealerId', 'formId'],
        },
      },
    },
  })
  requestBody: {dealerId: number, formId: number},
    @param.path.number('id') id: number,
  ): Promise<any> {
    const {dealerId, formId} = requestBody
    try {
      const found = await this.userProfileRepository.findById(dealerId)
      if (found && found.clientId === id && found?.information?.assessmentStartMonth) {

        const submissionFound = await this.userProfileRepository.dealerChecklistSubmissions(id).find({

          where: {
            dealerId: dealerId, type: 1, formId: formId
          },

          include: [{
            relation: 'dealer', scope: {where: {fields: {'information': true, 'dealerCode': true}}}
          }]
        })

        if (submissionFound.length) {
          const result: any = await this.getObjectWithHighestMonth(submissionFound)
          if (result && result?.dealerId) {

            return {status: 2, data: result, message: "Self Assessment Submitted"}

          } else {
            return {status: 1, data: [], message: "Self Assessment Not Found"}
          }

        } else {
          return {status: 1, data: [], message: "Self Assessment Not Submitted"}
        }
      } else if (found && found.clientId === id) {
        return {status: 0, data: [], message: "Self Assessment not started"}
      } else if (found && found.clientId !== id) {
        return {status: 0, data: [], message: "Invalid Enterprise User Login"}
      } else {
        return {status: 0, data: [], message: "Invalid Login"}

      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }
  @post('/user-profiles/{id}/dealer-checklist-recent-submissions-custom')
  async getDealerRecentSubmissionByDealerVendorId(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            dealerId: {
              type: 'number'
            },
            vendorId: {
              type: 'number'
            },
            formId: {
              type: 'number'
            }
          },
          required: ['dealerId', 'formId', 'vendorId'],
        },
      },
    },
  })
  requestBody: {dealerId: number, vendorId: number, formId: number},
    @param.path.number('id') id: number,
  ): Promise<any> {
    const {dealerId, formId, vendorId} = requestBody
    try {
      const found = await this.userProfileRepository.findById(dealerId)
      if (found && found.clientId === id) {

        const vendor = await this.vendorCodeRepository.findById(vendorId)
        if (vendor && vendor.assessmentStartMonth) {
          const submissionFound = await this.userProfileRepository.dealerChecklistSubmissions(id).find({

            where: {
              dealerId: dealerId, type: 1, formId: formId, vendorId
            },

            include: [{
              relation: 'dealer', scope: {where: {fields: {'information': true, 'dealerCode': true}}}
            }, {relation: 'vendor'}]
          })

          if (submissionFound.length) {
            const result: any = await this.getObjectWithHighestMonth(submissionFound)
            if (result && result?.dealerId) {

              return {status: 2, data: result, message: "Self Assessment Submitted"}

            } else {
              return {status: 1, data: [], message: "Self Assessment Not Found"}
            }

          } else {
            return {status: 1, data: [], message: "Self Assessment Not Submitted"}
          }
        } else if (vendor) {
          return {status: 0, data: [], message: "Self Assessment not triggered"}

        } else {
          return {status: 0, data: [], message: "Invalid Vendor Login"}
        }
      } else if (found && found.clientId !== id) {
        return {status: 0, data: [], message: "Invalid Enterprise User Login"}
      } else {
        return {status: 0, data: [], message: "Invalid Login"}

      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }
  async getObjectWithHighestMonth(data: any) {


    if (!data || data.length === 0) return null;

    let highestMonthObject = null;
    let highestDate: any = null;

    data.forEach((item: any) => {
      if (item.reporting_period && Array.isArray(item.reporting_period)) {
        item.reporting_period.forEach((period: string) => {
          const [month, year] = period.split("-").map(Number);
          const currentDate: any = new Date(year, month - 1); // Month is 0-based in JavaScript Date

          if (!highestDate || currentDate > highestDate) {

            highestDate = currentDate;
            highestMonthObject = item;
          } else {
            console.log('else')
          }
        });
      }
    });

    return highestMonthObject;
  }

}
