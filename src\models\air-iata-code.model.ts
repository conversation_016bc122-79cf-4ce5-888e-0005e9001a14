import {Entity, model, property} from '@loopback/repository';

@model()
export class AirIataCode extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  shortName?: string;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  lat?: string;

  @property({
    type: 'string',
  })
  long?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  created_on?: string;


  constructor(data?: Partial<AirIataCode>) {
    super(data);
  }
}

export interface AirIataCodeRelations {
  // describe navigational properties here
}

export type AirIataCodeWithRelations = AirIataCode & AirIataCodeRelations;
