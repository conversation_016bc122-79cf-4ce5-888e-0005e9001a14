import {inject} from '@loopback/core';
import {post, requestBody} from '@loopback/rest';
import {UserProfileController} from '../controllers';


export class SttReportHelper {
  constructor(
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController

  ) {

  }

  @post('validate-input-parameters', {
    responses: {
      '200': {
        description: 'STT Report Helper',
        content: {'application/json': {schema: {type: 'object'}}},
      },
    },
  })
  validateInputParameters(@requestBody({
    content: {
      'application/json': {
        required: true,
        schema: {

          type: 'object',
          properties: {
            "main_data_source": {
              type: 'string'
            },
            "sub_data_source": {
              type: 'string'
            },
            "raw_parameters": {
              type: 'object'
            }
          },
          required: [
            'main_data_source',
            'sub_data_source',
            'raw_parameters'
          ]
        },
      },
    },
  })
  inputParameters: any): any {
    const {main_data_source, sub_data_source, raw_parameters} = inputParameters;
    const mainDataSourceType = ['Quantitative'];
    const subDataSourceType = ['raw', 'indicator'];
    if (mainDataSourceType.includes(main_data_source)) {
      if (main_data_source === 'Quanitative') {
        if (sub_data_source === 'raw' && raw_parameters) {
          return 1
        } else if (sub_data_source === 'indicator' && raw_parameters) {
          return 2
        }
      } else if (main_data_source === 'Qualitative') {
      } else if (main_data_source === 'Config') {
      }



    }

  }




}
