import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AssignQlEntity,
  UserProfile,
} from '../models';
import {AssignQlEntityRepository, AssignQlEntityUserRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, QSectionRepository, QTopicRepository, UserProfileRepository} from '../repositories';
import {Helper} from '../services';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class UserProfileAssignQlEntityController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('services.HelperProvider')
    public helper: Helper,
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(QTopicRepository)
    public qTopicRepository: QTopicRepository,
    @repository(QSectionRepository)
    public qSectionRepository: QSectionRepository,
  ) { }

  @get('/user-profiles/{id}/assign-ql-entities', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignQlEntity',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignQlEntity)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignQlEntity>,
  ): Promise<AssignQlEntity[]> {
    return this.userProfileRepository.assignQlEntities(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-ql-entities', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignQlEntity)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntity, {
            title: 'NewAssignQlEntityInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignQlEntity: Omit<AssignQlEntity, 'id'>,
  ): Promise<AssignQlEntity> {
    const entityAssignment = await this.assignQlEntityRepository.findById(assignQlEntity.qCategoryId)

    // consolidation list
    const consolidatorData = await this.userProfileController.filteredUP({where: {id: {inq: entityAssignment?.consolidator_ids || []}}})

    const topic = (await this.qTopicRepository.findById(assignQlEntity.qTopicId)).name
    const section = (await this.qSectionRepository.findById(assignQlEntity.qSectionId)).name
    const adminObj = await this.userProfileRepository.findById(id);

    let entity: any[] = [];

    if (assignQlEntity.tier0_ids && assignQlEntity.tier0_ids.includes(0)) {
      entity = [{name: 'Corporate'}];
    }

    if (assignQlEntity.tier1_ids && assignQlEntity.tier1_ids.length > 0) {
      const tier1Locations = await this.locationOneRepository.find({
        where: {id: {inq: assignQlEntity.tier1_ids}},
      });
      entity.push(...tier1Locations);
    }

    if (assignQlEntity.tier2_ids && assignQlEntity.tier2_ids.length > 0) {
      const tier2Locations = await this.locationTwoRepository.find({
        where: {id: {inq: assignQlEntity.tier2_ids}},
      });
      entity.push(...tier2Locations);
    }

    if (assignQlEntity.tier3_ids && assignQlEntity.tier3_ids.length > 0) {
      const tier3Locations = await this.locationThreeRepository.find({
        where: {id: {inq: assignQlEntity.tier3_ids}},
      });
      entity.push(...tier3Locations);
    }


    for (const user of consolidatorData) {
      const body = `
      <p><strong>Dear ${user?.information?.empname || 'User'},</strong></p>

      <p style="margin: 5px 0px;">
        You have been assigned as the <strong>Data Consolidator</strong> for the qualitative section
        <strong>"${section}"</strong> under the topic
        <strong>"${topic}"</strong> for the reporting entities
        <strong>"${entity.map((x: any) => x.name).join(', ')}"</strong> on the <strong>Navigos ESG</strong> platform.
      </p>


    ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
        To access the form and begin your response, please log in to the platform using the following link:
        <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a>
      </p>` : ''}

      <p style="margin: 5px 0px;">
        If you have any questions or require assistance, you may raise a ticket through the platform or
        contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
      </p>

      <p style="margin: 5px 0px;">Thank you for your cooperation.</p>

      <p style="margin: 5px 0px; color: gray; font-size: 0.9em;">
        This is an automated notification. Please do not reply to this message.
      </p>
      `;
      const subject = 'Assignment of Sustainability Qualitative Response Form(s) for Consolidation – Navigos'

      try {
        const info = await this.sqsService.sendEmail(user.email, subject, body, []).then((info) => {
          console.log('mail sent')

        }).catch((err) => {
          console.log('error in sending')

        })
      } catch (error) {
        console.error('Error sending email:', error);
        throw new Error('Failed to send email');
      }
    }

    return this.userProfileRepository.assignQlEntities(id).create(assignQlEntity);
  }

  @patch('/user-profiles/{id}/assign-ql-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignQlEntity PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntity, {partial: true}),
        },
      },
    })
    assignQlEntity: Partial<AssignQlEntity>,
    @param.query.object('where', getWhereSchemaFor(AssignQlEntity)) where?: Where<AssignQlEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignQlEntities(id).patch(assignQlEntity, where);
  }

  @del('/user-profiles/{id}/assign-ql-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignQlEntity DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignQlEntity)) where?: Where<AssignQlEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignQlEntities(id).delete(where);
  }
}
