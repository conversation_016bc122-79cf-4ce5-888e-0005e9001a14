import { AuthenticationStrategy, AuthenticationBindings } from '@loopback/authentication';
import { HttpErrors, Request } from '@loopback/rest';
import { securityId, UserProfile } from '@loopback/security';
import { verify, decode } from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import { inject, bind, BindingScope } from '@loopback/core';
const AWS = require('aws-sdk');
const axios = require('axios');
AWS.config.update({
    region: process.env.AWS_REGION,
    credentials: new AWS.CognitoIdentityCredentials({
        IdentityPoolId: process.env.AWS_EXTERNAL_USER_POOL_ID
    })
});

interface Credentials {
    accessToken: string;
    idToken: string;
}


const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();

async function getUserAttributes(accessToken: any) {
    const params = {
        AccessToken: accessToken
    };

    try {
        const response = await cognitoIdentityServiceProvider.getUser(params).promise();
        return response.UserAttributes.reduce((acc: { [x: string]: any; }, attr: { Name: string | number; Value: any; }) => {
            acc[attr.Name] = attr.Value;
            return acc;
        }, {});
    } catch (error) {
        console.error('Error fetching user attributes:', error);
        throw error;
    }
}

@bind({ tags: { namespace: AuthenticationBindings.STRATEGY } })
export class CognitoJwtAuthenticationStrategy implements AuthenticationStrategy {
    name = 'cognito-jwt';

    async authenticate(request: Request): Promise<UserProfile | undefined> {
        const token: Credentials = this.extractCredentials(request);
        let email = '';
        if (!token) {
            return undefined;
        }

        const jwtKeyset = jwksClient({
            jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.AWS_EXTERNAL_USER_POOL_ID}/.well-known/jwks.json`,
        })

        const decoded = decode(token.accessToken, { complete: true });
        const idDecoded = decode(token.idToken, { complete: true });

        if (idDecoded) {


            const payload = idDecoded.payload;


            if (typeof payload === 'object' && payload !== null) {
                email = payload.email || '';
            }

            if (!decoded || typeof decoded === 'string' || !decoded.header) {
                return undefined;
            }

        }


        if (!decoded || typeof decoded === 'string' || !decoded.header) {
            return undefined;
        }



        const signingKey = await jwtKeyset.getSigningKey(decoded.header.kid);
        const publicKey = signingKey.getPublicKey();
        const audience = `${process.env.AWS_EXTERNAL_CLIENT_ID}`
        try {

            const userProfile = verify(token.accessToken, publicKey, {
                algorithms: ['RS256'],

                issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
            }) as UserProfile;
            if (userProfile['client_id'] !== process.env.AWS_EXTERNAL_CLIENT_ID) {
                throw new HttpErrors.Unauthorized('Invalid token client ID');
            }
            userProfile[securityId] = userProfile.sub;
            userProfile.email = email
            console.log(userProfile)
            return userProfile;
        } catch (err) {
            console.log(err)
            return err;
        }
    }

    extractCredentials(request: Request): Credentials {
        if (!request.headers.authorization) {
            throw new HttpErrors.Unauthorized('Authorization header is missing');
        }

        const authHeaderValue = request.headers.authorization;
        if (!authHeaderValue.startsWith('Bearer ')) {
            throw new HttpErrors.Unauthorized('Authorization header is not of type Bearer');
        }
        const accessToken = authHeaderValue.replace('Bearer ', '');

        const idTokenHeaderName = Object.keys(request.headers).find(
            key => key.toLowerCase() === 'x-id-token'
        );
        if (!idTokenHeaderName || !request.headers[idTokenHeaderName]) {
            throw new HttpErrors.Unauthorized('X-Id-Token header is missing');
        }
        const idToken = request.headers[idTokenHeaderName];

        return {
            accessToken: accessToken,
            idToken: `${idToken}`
        };
    }


}
