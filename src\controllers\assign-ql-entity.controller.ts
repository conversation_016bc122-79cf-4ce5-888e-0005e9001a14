import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignQlEntity} from '../models';
import {AssignQlEntityRepository, AssignQlEntityUserRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, QSectionRepository, QTopicRepository, UserProfileRepository} from '../repositories';
import {Helper} from '../services';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class AssignQlEntityController {
  constructor(
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
    @inject('services.HelperProvider')
    public helper: Helper,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(QTopicRepository)
    public qTopicRepository: QTopicRepository,
    @repository(QSectionRepository)
    public qSectionRepository: QSectionRepository,
  ) { }

  // Helper method to check if two arrays have the same elements
  arraysEqual(a: number[], b: number[]): boolean {
    if (a.length !== b.length) return false;

    // Sort both arrays to ensure consistent comparison
    const sortedA = [...a].sort((x, y) => x - y);
    const sortedB = [...b].sort((x, y) => x - y);

    // Compare each element
    for (let i = 0; i < sortedA.length; i++) {
      if (sortedA[i] !== sortedB[i]) return false;
    }

    return true;
  }

  @post('/assign-ql-entities')
  @response(200, {
    description: 'AssignQlEntity model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignQlEntity)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntity, {
            title: 'NewAssignQlEntity',
            exclude: ['id'],
          }),
        },
      },
    })
    assignQlEntity: Omit<AssignQlEntity, 'id'>,
  ): Promise<AssignQlEntity> {
    return this.assignQlEntityRepository.create(assignQlEntity);
  }

  @get('/assign-ql-entities/count')
  @response(200, {
    description: 'AssignQlEntity model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignQlEntity) where?: Where<AssignQlEntity>,
  ): Promise<Count> {
    return this.assignQlEntityRepository.count(where);
  }

  @get('/assign-ql-entities')
  @response(200, {
    description: 'Array of AssignQlEntity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignQlEntity, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignQlEntity) filter?: Filter<AssignQlEntity>,
  ): Promise<AssignQlEntity[]> {
    return this.assignQlEntityRepository.find(filter);
  }

  @patch('/assign-ql-entities')
  @response(200, {
    description: 'AssignQlEntity PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntity, {partial: true}),
        },
      },
    })
    assignQlEntity: AssignQlEntity,
    @param.where(AssignQlEntity) where?: Where<AssignQlEntity>,
  ): Promise<Count> {
    return this.assignQlEntityRepository.updateAll(assignQlEntity, where);
  }

  @get('/assign-ql-entities/{id}')
  @response(200, {
    description: 'AssignQlEntity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignQlEntity, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignQlEntity, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignQlEntity>
  ): Promise<AssignQlEntity> {
    return this.assignQlEntityRepository.findById(id, filter);
  }

  @patch('/assign-ql-entities/{id}')
  @response(204, {
    description: 'AssignQlEntity PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntity, {partial: true}),
        },
      },
    })
    assignQlEntity: AssignQlEntity,
  ): Promise<void> {

    const existingEntity = await this.assignQlEntityRepository.findById(id);
    await this.assignQlEntityRepository.updateById(id, assignQlEntity);
    const entityUserAssignment = await this.assignQlEntityUserRepository.find({where: {entityAssId: id}})

    interface ReporterInfo {
      "Reporting Entity": string;
      "Reporter's Submission Due Date": string | null;
      "Reporter Name": string[];
    }

    let reporterMappings: ReporterInfo[] = [];

    for (const assignment of entityUserAssignment) {
      const {level, locationId, due_date, reporter_ids} = assignment;
      let entity = '';

      if (level === 0) {
        entity = 'Corporate';
      } else if (level === 1 && locationId) {
        const loc = await this.locationOneRepository.findById(locationId);
        entity = loc?.name || '';
      } else if (level === 2 && locationId) {
        const loc = await this.locationTwoRepository.findById(locationId);
        entity = loc?.name || '';
      } else if (level === 3 && locationId) {
        const loc = await this.locationThreeRepository.findById(locationId);
        entity = loc?.name || '';
      }

      if (entity) {
        let reporterNames: string[] = [];
        if (reporter_ids?.length) {
          const reporterProfiles = await this.userProfileRepository.find({
            where: {id: {inq: reporter_ids}},
          });
          reporterNames = reporterProfiles.map(user => user.information?.empname || '—');
        }

        const formattedDueDate = due_date
          ? new Date(due_date).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          }) // e.g., "20 May 2025"
          : null;

        reporterMappings.push({
          "Reporting Entity": entity,
          "Reporter Name": reporterNames,
          "Reporter's Submission Due Date": formattedDueDate ?? null,

        });
      }

    }


    // Step 2: Compare consolidator_ids
    const oldIds = (existingEntity.consolidator_ids || []).sort();
    const newIds = (assignQlEntity.consolidator_ids || []).sort();

    // Determine topic and section (use updated or fallback to existing)
    const topicId = assignQlEntity.qTopicId ?? existingEntity.qTopicId;
    const sectionId = assignQlEntity.qSectionId ?? existingEntity.qSectionId;

    const topic = (await this.qTopicRepository.findById(topicId)).name;
    const section = (await this.qSectionRepository.findById(sectionId)).name;

    const adminObj = await this.userProfileRepository.findById(existingEntity.userProfileId);

    // New: Compare topic and section changes
    const topicChanged =
      assignQlEntity.qTopicId !== undefined &&
      assignQlEntity.qTopicId !== existingEntity.qTopicId;

    const sectionChanged =
      assignQlEntity.qSectionId !== undefined &&
      assignQlEntity.qSectionId !== existingEntity.qSectionId;

    const isEntityAssignmentChanged =
      // Check tier0_ids changes
      (existingEntity.tier0_ids?.length !== assignQlEntity.tier0_ids?.length ||
        !this.arraysEqual(existingEntity.tier0_ids || [], assignQlEntity.tier0_ids || [])) ||
      // Check tier1_ids changes
      (existingEntity.tier1_ids?.length !== assignQlEntity.tier1_ids?.length ||
        !this.arraysEqual(existingEntity.tier1_ids || [], assignQlEntity.tier1_ids || [])) ||
      // Check tier2_ids changes
      (existingEntity.tier2_ids?.length !== assignQlEntity.tier2_ids?.length ||
        !this.arraysEqual(existingEntity.tier2_ids || [], assignQlEntity.tier2_ids || [])) ||
      // Check tier3_ids changes
      (existingEntity.tier3_ids?.length !== assignQlEntity.tier3_ids?.length ||
        !this.arraysEqual(existingEntity.tier3_ids || [], assignQlEntity.tier3_ids || []));

    const isChanged =
      oldIds.length !== newIds.length ||
      !oldIds.every((value, index) => value === newIds[index]) ||
      topicChanged ||
      sectionChanged ||
      isEntityAssignmentChanged;
    console.log(isEntityAssignmentChanged)
    if (isChanged) {
      // Step 3: Fetch new consolidator user profiles
      const newConsolidatorData = await this.userProfileController.filteredUP({
        where: {id: {inq: assignQlEntity.consolidator_ids || []}},
      });

      let entity: any[] = [];

      if (assignQlEntity.tier0_ids?.includes(0)) {
        entity = [{name: 'Corporate'}];
      }

      if (assignQlEntity.tier1_ids?.length) {
        const tier1Locations = await this.locationOneRepository.find({
          where: {id: {inq: assignQlEntity.tier1_ids}},
        });
        entity.push(...tier1Locations);
      }

      if (assignQlEntity.tier2_ids?.length) {
        const tier2Locations = await this.locationTwoRepository.find({
          where: {id: {inq: assignQlEntity.tier2_ids}},
        });
        entity.push(...tier2Locations);
      }

      if (assignQlEntity.tier3_ids?.length) {
        const tier3Locations = await this.locationThreeRepository.find({
          where: {id: {inq: assignQlEntity.tier3_ids}},
        });
        entity.push(...tier3Locations);
      }

      // Step 4: Send email notification to each new consolidator
      for (const user of newConsolidatorData) {
        const body = `
      <p><strong>Dear ${user?.information?.empname || 'User'},</strong></p>

      <p style="margin: 5px 0px;">
        You have been assigned as the <strong>Data Consolidator</strong> for the qualitative section
        <strong>"${section}"</strong> under the topic
        <strong>"${topic}"</strong> for the reporting entities
        <strong>"${entity.map((x: any) => x.name).join(', ')}"</strong> on the <strong>Navigos ESG</strong> platform.
      </p>

      <p style="margin: 5px 0px;"><strong>Please find the assignment details below:</strong></p>


${this.helper.generateHtmlTable(reporterMappings)}

      ${adminObj?.userPortalUrl
            ? `<p style="margin: 5px 0px;">
          To access the form and begin your response, please log in to the platform using the following link:
          <a href=${adminObj.userPortalUrl} target="_blank">EiSqr – ESG Platform</a>
        </p>`
            : ''
          }

      <p style="margin: 5px 0px;">
        If you have any questions or require assistance, you may raise a ticket through the platform or
        contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
      </p>

      <p style="margin: 5px 0px;">Thank you for your cooperation.</p>

      <p style="margin: 5px 0px; color: gray; font-size: 0.9em;">
        This is an automated notification. Please do not reply to this message.
      </p>
    `;
        const subject =
          'Assignment of Sustainability Qualitative Response Form(s) for Consolidation – Navigos';

        try {
          const info = await this.sqsService.sendEmail(user.email, subject, body, []).then((info) => {
            console.log('mail sent')

          }).catch((err) => {
            console.log('error in sending')

          })

        } catch (error) {
          console.error('Error sending consolidation update email:', error);
        }
      }
    }


  }
  @patch('/assign-ql-entities-response/{id}')
  @response(204, {
    description: 'AssignQlEntity PATCH success',
  })
  async updateResponseById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntity, {partial: true}),
        },
      },
    })
    assignQlEntity: AssignQlEntity,
  ): Promise<any> {
    const existingResponse = await this.assignQlEntityRepository.findById(id);
    if (existingResponse) {

      const response = existingResponse?.response?.Consolidate || {}
      await this.assignQlEntityRepository.updateById(id, {response: {Consolidate: {...response, ...assignQlEntity.response}}});
      return await this.assignQlEntityRepository.findById(id);
    }
  }
  @put('/assign-ql-entities/{id}')
  @response(204, {
    description: 'AssignQlEntity PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignQlEntity: AssignQlEntity,
  ): Promise<void> {
    await this.assignQlEntityRepository.replaceById(id, assignQlEntity);
  }

  @del('/assign-ql-entities/{id}')
  @response(204, {
    description: 'AssignQlEntity DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignQlEntityRepository.deleteById(id);
  }
}
