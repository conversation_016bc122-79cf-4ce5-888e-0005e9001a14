{"name": "apiv1", "version": "0.0.1", "description": "apiv1", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "14 || 16 || 17 || 18"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t apiv1 .", "docker:run": "docker run -p 3000:3000 -d apiv1", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@loopback/authentication": "^10.1.5", "@loopback/authentication-jwt": "^0.14.5", "@loopback/boot": "^6.1.5", "@loopback/core": "^5.1.5", "@loopback/repository": "^6.1.5", "@loopback/rest": "^13.1.5", "@loopback/rest-explorer": "^6.1.5", "@loopback/service-proxy": "^6.1.5", "aws-sdk": "^2.1534.0", "axios": "^1.6.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "generate-password": "^1.7.0", "isemail": "^3.2.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "loopback-connector-mongodb": "^5.6.0", "loopback-connector-mysql": "^5.4.4", "luxon": "^3.4.4", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.8.0", "parquetjs-lite": "^0.8.7", "tslib": "^2.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@loopback/build": "^10.1.5", "@loopback/eslint-config": "^14.0.5", "@loopback/testlab": "^6.1.5", "@types/luxon": "^3.4.2", "@types/multer": "^1.4.7", "@types/node": "^16.18.70", "@types/nodemailer": "^6.4.6", "@types/uuid": "^9.0.3", "eslint": "^8.51.0", "source-map-support": "^0.5.21", "typescript": "~5.2.2"}}