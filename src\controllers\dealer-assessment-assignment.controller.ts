import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {DealerAssessmentAssignment} from '../models';
import {DealerAssessmentAssignmentRepository, DealerAuditorChecklistSubmissionRepository, UserProfileRepository, UserRoleAuthorizationRepository} from '../repositories';

export class DealerAssessmentAssignmentController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(DealerAuditorChecklistSubmissionRepository)
    public dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
  ) { }

  @post('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAssessmentAssignment)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {
            title: 'NewDealerAssessmentAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    dealerAssessmentAssignment: Omit<DealerAssessmentAssignment, 'id'>,
  ): Promise<DealerAssessmentAssignment> {
    return this.dealerAssessmentAssignmentRepository.create(dealerAssessmentAssignment);
  }

  @get('/dealer-assessment-assignments/count', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(DealerAssessmentAssignment) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.count(where);
  }

  @get('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'Array of DealerAssessmentAssignment model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DealerAssessmentAssignment, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(DealerAssessmentAssignment) filter?: Filter<DealerAssessmentAssignment>,
  ): Promise<DealerAssessmentAssignment[]> {
    return this.dealerAssessmentAssignmentRepository.find(filter);
  }

  @patch('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: DealerAssessmentAssignment,
    @param.where(DealerAssessmentAssignment) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.updateAll(dealerAssessmentAssignment, where);
  }

  @get('/dealer-assessment-assignments/{id}', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerAssessmentAssignment, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DealerAssessmentAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerAssessmentAssignment>
  ): Promise<DealerAssessmentAssignment> {
    return this.dealerAssessmentAssignmentRepository.findById(id, filter);
  }

  @patch('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: DealerAssessmentAssignment,
  ): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.updateById(id, dealerAssessmentAssignment);
  }

  @put('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dealerAssessmentAssignment: DealerAssessmentAssignment,
  ): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.replaceById(id, dealerAssessmentAssignment);
  }

  @del('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.deleteById(id);
  }

  @post('/dealer-auditor-checklist-submissions-status')
  async getAssignment(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            userId: {
              type: 'number'
            },
            archive: {type: 'boolean', nullable: true},

          },
          required: ['userId'],
        },
      },
    },
  })
  requestBody: {userId: number, archive: boolean}
  ): Promise<any> {
    const {userId, archive} = requestBody
    try {
      const found = await this.userProfileRepository.findById(userId)
      if (found && found.clientId) {
        const roleCheck = await this.userRoleAuthorizationRepository.find({where: {user_id: userId, userProfileId: found.clientId}})
        if (roleCheck && roleCheck.length && roleCheck.some(x => x.roles?.includes(19))) {
          const assignmentList = await this.userProfileRepository.dealerAssessmentAssignments(found.clientId).find({"include": [{"relation": "dealer", "scope": {"fields": {"information": true, "dealerCode": true}}}, {relation: "vendor"}, {relation: "dealerAuditorChecklistSubmission"}]})
          if (assignmentList.length && assignmentList.some(x => x.assessors?.includes(userId))) {
            const assignedList = assignmentList.filter(x => x.assessors?.includes(userId) && (!x.dealerAuditorChecklistSubmission || !x?.dealerAuditorChecklistSubmission?.type))
            return {status: 2, archiveData: archive ? assignmentList.filter(x => x.assessors?.includes(userId) && (x.dealerAuditorChecklistSubmission && x.dealerAuditorChecklistSubmission?.type === 1)) : [], data: assignedList.map((x: any) => ({...x, maskId: 'MSI-' + (x?.vendor?.code || 'NA') + '-' + DateTime.fromISO(x.created_on, {zone: 'Asia/Calcutta'}).toFormat('yyyyMd')})), message: 'Checklist Found'}
          } else {
            return {status: 1, data: [], message: 'Checklist is not assigned'}
          }

        } else {
          return {status: 0, data: [], message: 'You are not authorized as MSI Dealer Accessor'}
        }
      } else {
        return {status: 0, data: [], message: 'Invalid Dealer'}
      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }
}
