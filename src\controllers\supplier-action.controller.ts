import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {SupplierAction} from '../models';
import {
  SupplierActionRepository,
  SupplierAssessmentAssignmentRepository,
  UserProfileRepository,
  UserRoleAuthorizationRepository,
  VendorCodeRepository
} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class SupplierActionController {
  constructor(
    @repository(SupplierActionRepository)
    public supplierActionRepository: SupplierActionRepository,
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,

  ) { }

  @post('/supplier-actions')
  @response(200, {
    description: 'SupplierAction model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAction)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {
            title: 'NewSupplierAction',
            exclude: ['id'],
          }),
        },
      },
    })
    supplierAction: Omit<SupplierAction, 'id'>,
  ): Promise<SupplierAction> {
    return this.supplierActionRepository.create(supplierAction);
  }

  @get('/supplier-actions/count')
  @response(200, {
    description: 'SupplierAction model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierAction) where?: Where<SupplierAction>,
  ): Promise<Count> {
    return this.supplierActionRepository.count(where);
  }

  @get('/supplier-actions')
  @response(200, {
    description: 'Array of SupplierAction model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierAction, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierAction) filter?: Filter<SupplierAction>,
  ): Promise<SupplierAction[]> {
    return this.supplierActionRepository.find(filter);
  }

  @patch('/supplier-actions')
  @response(200, {
    description: 'SupplierAction PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {partial: true}),
        },
      },
    })
    supplierAction: SupplierAction,
    @param.where(SupplierAction) where?: Where<SupplierAction>,
  ): Promise<Count> {
    return this.supplierActionRepository.updateAll(supplierAction, where);
  }

  @get('/supplier-actions/{id}')
  @response(200, {
    description: 'SupplierAction model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierAction, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplierAction, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierAction>
  ): Promise<SupplierAction> {
    return this.supplierActionRepository.findById(id, filter);
  }

  @patch('/supplier-actions/{id}')
  @response(204, {
    description: 'SupplierAction PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {partial: true}),
        },
      },
    })
    supplierAction: SupplierAction,
  ): Promise<void> {
    await this.supplierActionRepository.updateById(id, supplierAction);
  }

  @put('/supplier-actions/{id}')
  @response(204, {
    description: 'SupplierAction PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplierAction: SupplierAction,
  ): Promise<void> {
    await this.supplierActionRepository.replaceById(id, supplierAction);
  }

  @del('/supplier-actions/{id}')
  @response(204, {
    description: 'SupplierAction DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplierActionRepository.deleteById(id);
  }


  @post('/submit-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanSubmission(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                rootCause: {type: 'string'},
                proposedCorrectiveAction: {type: 'string'},
                actionTargetDate: {type: 'string'}
                // Additional properties are allowed by default
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && (existing.type === 12 || existing.type === 102)) {
        const {rootCause, proposedCorrectiveAction, actionTargetDate} = update
        let newObj = {
          rootCause, proposedCorrectiveAction, actionTargetDate, type: 21, actionPlanRejectedBy: null, actionPlanRejectedOn: null
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType and submission date
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          const currentDate = new Date().toISOString();
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 21,
              actionPlanSubmittedDate: currentDate
            }
          );
        }

        updatedCount++;
      }
    }

    return {count: updatedCount};
  }

  @post('/draft-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanDraft(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                rootCause: {type: 'string'},
                proposedCorrectiveAction: {type: 'string'},
                actionTargetDate: {
                  oneOf: [
                    {type: 'string'},
                    {type: 'null'}
                  ]
                }
                // Additional properties are allowed by default
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && (existing.type === 12 || existing.type === 102)) {
        const {rootCause, proposedCorrectiveAction, actionTargetDate} = update
        let newObj: any = {
          type: 102, actionPlanRejectedBy: null, actionPlanRejectedOn: null
        }
        if (actionTargetDate) {
          newObj['actionTargetDate'] = actionTargetDate
        }
        if (rootCause) {
          newObj['rootCause'] = rootCause
        }
        if (proposedCorrectiveAction) {
          newObj['proposedCorrectiveAction'] = proposedCorrectiveAction
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType and submission date
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          const currentDate = new Date().toISOString();
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 102,
              actionPlanSubmittedDate: currentDate
            }
          );
        }

        updatedCount++;
      }
    }

    return {count: updatedCount};
  }

  @post('/reject-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanRejection(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                actionPlanRejectedBy: {type: 'number'},
                actionPlanRejectedOn: {type: 'string'},
                actionPlanApproverComments: {type: 'string'}
                // Additional properties are allowed
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && existing.type === 21) {
        const {actionPlanRejectedBy, actionPlanRejectedOn, actionPlanApproverComments} = update
        let newObj = {
          actionPlanRejectedBy, actionPlanRejectedOn, actionPlanApproverComments, type: 12
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 12,
              // Reset the submission date since it was rejected
              actionPlanSubmittedDate: null
            }
          );
        }

        updatedCount++;
      }
    }

    return {count: updatedCount};
  }
  @post('/approve-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanApproval(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                actionPlanApprovedBy: {type: 'number'},
                actionPlanApprovedOn: {type: 'string'},
                actionPlanApproverComments: {type: 'string'}
                // Additional properties are allowed
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;
    const update = updates[0]; // assuming updates has only one item, or you only care about the first
    const supplierAction = await this.supplierActionRepository.findById(update.id);
    if (!supplierAction) return {count: 0};

    const supplierAssessmentId = supplierAction.supplierAssessmentAssignmentId;

    const assessmentAssignment = await this.supplierAssessmentAssignmentRepository.findById(
      supplierAssessmentId,
      {
        include: [
          {
            relation: 'auditorAssignmentSubmission',
            scope: {
              fields: {
                type: true,
                approved_on: true,
                approverComments: true,
                rejected_on: true,
                auditorMSIScore: true,
                submitted_on: true,
                modified_on: true,
                id: true,
              },
            },
          },
          {relation: 'vendor'},
        ],
      }
    );
    const roleAgainsCategory = {
      1: 25,
      2: 26,
      3: 27,
      4: 28,
      5: 29,
      6: 30,
      7: 31,
      8: 32,
      9: 33,
      10: 34,
      11: 35
    }
    const vendorData = await this.vendorCodeRepository.findById(assessmentAssignment.vendorId);
    const role_id = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
    const roles = await this.userRoleAuthorizationRepository.execute(

      `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
      [JSON.stringify([role_id])]
    )
    const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
    const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
    const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
    const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
    const approveDate = DateTime.fromISO(assessmentAssignment?.auditorAssignmentSubmission.approved_on ?? '').toFormat('dd-MM-yyyy');
    const adminObj = await this.userProfileRepository.findById(289);
    const supplierOtherSpoc = this.userProfileController.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
    const supplierMailIds = [...supplierOtherSpoc, vendorSpoc?.[0]?.email].filter((x: any) => x)
    if (supplierAction.supplierAssessmentAssignmentId) {


      const subject = ` Acknowledgement on the Approval of Submitted Action Plan - ${vendorData.supplierName} (${vendorData.code})`;
      const body = `<p>Dear ${vendorData.supplierName},</p>
 <p style="margin: 10px 0px;">Hope you are doing well.</p>

<p style="margin: 10px 0px;">
This is to acknowledge the receipt of your Corrective Action Plan (CAP) and Root Cause Analysis (RCA) submitted via the Navigos Sustainability Platform in response to the audit report shared on ${approveDate}.</p>

<p style="margin: 10px 0px;"> We have reviewed your submission and are pleased to inform you that the Action Plan has been approved. Please proceed with implementing the agreed actions and updating the status and evidence of closures on the platform as per the timelines indicated.</p>

 ${adminObj?.userPortalUrl ? `<p style="margin: 10px 0px;">
   please log in to the  <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a> to complete and submit the required action
 </p>` : ''}

<p style="margin: 10px 0px;"> Should you require any further assistance, please feel free to reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a>, copying <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

<p style="margin: 10px 0px;">We appreciate your continued commitment to sustainability and collaboration in this process.</p>

<p style="margin: 10px 0px;">
Warm regards,<br/>
TVS Motor Company Limited</p>

<p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>

    `;

      try {
        await this.sqsService.sendEmail([...supplierMailIds].filter(y => y), subject, body, [...headSpocMailId, '<EMAIL>', '<EMAIL>'])
          .then(info => console.log('Email sent:', info))
          .catch(err => console.error('Error sending email:', err));
      } catch (error) {
        console.error('Error sending email:', error);
        throw new Error('Failed to send email');
      }
    }

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && existing.type === 21) {
        const {actionPlanApprovedBy, actionPlanApprovedOn, actionPlanApproverComments} = update
        let newObj = {
          actionPlanApprovedBy, actionPlanApprovedOn, actionPlanApproverComments, type: 1, actionPlanRejectedBy: null, actionPlanRejectedOn: null
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType and approval date
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          const currentDate = new Date().toISOString();
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 1,
              actionPlanApprovedDate: currentDate
            }
          );
        }

        updatedCount++;
      }
    }

    return {count: updatedCount};
  }

}
