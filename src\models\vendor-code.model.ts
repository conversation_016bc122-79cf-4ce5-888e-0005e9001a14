import {Entity, model, property} from '@loopback/repository';

@model()
export class VendorCode extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  code?: string;

  @property({
    type: 'number',
  })
  clientId?: number;

  @property({
    type: 'number',
  })
  emailSentCount?: number;

  @property({
    type: 'number',
  })
  plantLocation?: number;

  @property({
    type: 'number',
  })
  supplierCategory?: number;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierPriorityGroup?: number | null;
  @property({
    type: 'string',
  })
  supplierContact?: string;

  @property({
    type: 'string',
  })
  supplierName?: string;

  @property({
    type: 'string',
  })
  supplierSPOC?: string;

  @property({
    type: 'string',
  })
  supplierLocation?: string;

  @property({
    type: 'any',
  })
  supplierSpentOn?: any;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierContact2?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierContact3?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierEmail2?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierEmail3?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true, // allow null in OpenAPI schema
    },
  })
  dealerName?: string | null;

  @property({
    type: 'string',
  })
  dealerSPOC?: string;

  @property({
    type: 'number',
  })
  dealerCategory?: number;
  @property({
    type: 'number',
  })
  dealerZone?: number;
  @property({
    type: 'string',
  })
  dealerLocation?: string;
  @property({
    type: 'string',
  })
  dealerCountry?: string;
  @property({
    type: 'string',
  })
  dealerAO?: string;

  @property({
    type: 'object',
    default: null,
    required: false,
  })
  service: {
    areaManagerName: string | null;
    areaManagerMailId: string | null;
    zonalPlannerName: string | null;
    zonalPlannerMailId: string | null;
    regionalManagerName: string | null;
    regionalManagerMailId: string | null;
  }
  @property({
    type: 'object',
    default: null,
    required: false,
  })
  sales: {
    areaManagerName: string | null;
    areaManagerMailId: string | null;
    zonalPlannerName: string | null;
    zonalPlannerMailId: string | null;
    regionalManagerName: string | null;
    regionalManagerMailId: string | null;
  }
  @property({
    type: 'object',
    default: null,
    required: false,
  })
  aps: {
    areaManagerName: string | null;
    areaManagerMailId: string | null;
    regionalManagerName: string | null;
    regionalManagerMailId: string | null;
    hoPlannerName: string | null;
    hoPlannerMailId: string | null;
  }
  @property({
    type: 'object',
    default: null,
    required: false,
  })
  ao: {
    areaCommercialManagerName: string | null;
    areaCommercialManagerMailId: string | null;
    regionalCommercialManagerName: string | null;
    regionalCommercialManagerMailId: string | null;
  }
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  created_on?: string | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  assessmentStartMonth?: string | null;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  constructor(data?: Partial<VendorCode>) {
    super(data);
  }
}

export interface VendorCodeRelations {
  // describe navigational properties here
}

export type VendorCodeWithRelations = VendorCode & VendorCodeRelations;
