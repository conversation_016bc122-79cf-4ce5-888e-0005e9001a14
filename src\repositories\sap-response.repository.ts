import {DefaultCrudRepository} from '@loopback/repository';
import {SapResponse, SapResponseRelations} from '../models';
import {MysqlDataSource} from '../datasources';
import {inject} from '@loopback/core';

export class SapResponseRepository extends DefaultCrudRepository<
  SapResponse,
  typeof SapResponse.prototype.id,
  SapResponseRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SapResponse, dataSource);
  }
}
