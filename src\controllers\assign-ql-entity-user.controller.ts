import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {AssignQlEntityUser} from '../models';
import {AssignQlEntityRepository, AssignQlEntityUserRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, QSectionRepository, QTopicRepository, UserProfileRepository} from '../repositories';
import {Helper} from '../services';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class AssignQlEntityUserController {
  constructor(
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @inject('services.HelperProvider')
    public helper: Helper,
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(QTopicRepository)
    public qTopicRepository: QTopicRepository,
    @repository(QSectionRepository)
    public qSectionRepository: QSectionRepository,
  ) { }

  @post('/assign-ql-entity-users')
  @response(200, {
    description: 'AssignQlEntityUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignQlEntityUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {
            title: 'NewAssignQlEntityUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignQlEntityUser: Omit<AssignQlEntityUser, 'id'>,
  ): Promise<AssignQlEntityUser> {
    return this.assignQlEntityUserRepository.create(assignQlEntityUser);
  }

  @get('/assign-ql-entity-users/count')
  @response(200, {
    description: 'AssignQlEntityUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignQlEntityUser) where?: Where<AssignQlEntityUser>,
  ): Promise<Count> {
    return this.assignQlEntityUserRepository.count(where);
  }

  @get('/assign-ql-entity-users')
  @response(200, {
    description: 'Array of AssignQlEntityUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignQlEntityUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignQlEntityUser) filter?: Filter<AssignQlEntityUser>,
  ): Promise<AssignQlEntityUser[]> {
    return this.assignQlEntityUserRepository.find(filter);
  }

  @patch('/assign-ql-entity-users')
  @response(200, {
    description: 'AssignQlEntityUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {partial: true}),
        },
      },
    })
    assignQlEntityUser: AssignQlEntityUser,
    @param.where(AssignQlEntityUser) where?: Where<AssignQlEntityUser>,
  ): Promise<Count> {
    return this.assignQlEntityUserRepository.updateAll(assignQlEntityUser, where);
  }

  @get('/assign-ql-entity-users/{id}')
  @response(200, {
    description: 'AssignQlEntityUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignQlEntityUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignQlEntityUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignQlEntityUser>
  ): Promise<AssignQlEntityUser> {
    return this.assignQlEntityUserRepository.findById(id, filter);
  }

  @patch('/assign-ql-entity-users/{id}')
  @response(204, {
    description: 'AssignQlEntityUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {partial: true}),
        },
      },
    })
    assignQlEntityUser: AssignQlEntityUser,
  ): Promise<void> {
    // Step 1: Get old assignment data
    const oldData = await this.assignQlEntityUserRepository.findById(id);
    await this.assignQlEntityUserRepository.updateById(id, assignQlEntityUser);

    if (assignQlEntityUser.reporter_ids) {
      // Step 2: Determine new reporters
      const oldReporters = oldData.reporter_ids || [];
      const newReporters = assignQlEntityUser.reporter_ids || [];
      const addedReporters = newReporters.filter(id => !oldReporters.includes(id));

      const entityAssignment = await this.assignQlEntityRepository.findById(oldData.entityAssId)



      // consolidation list
      const consolidatorData = await this.userProfileController.filteredUP({where: {id: {inq: entityAssignment?.consolidator_ids || []}}})

      // Step 4: Fetch user info
      const addedReporterProfiles = await this.userProfileController.filteredUP({
        where: {id: {inq: addedReporters}}
      });


      const topic = (await this.qTopicRepository.findById(assignQlEntityUser.qTopicId || oldData.qTopicId)).name;
      const section = (await this.qSectionRepository.findById(assignQlEntityUser.qSectionId || oldData.qSectionId)).name;
      const adminObj = await this.userProfileRepository.findById(oldData.userProfileId);

      // Step 5: Determine entity name
      const level = assignQlEntityUser.level ?? oldData.level;
      const locationId = assignQlEntityUser.locationId ?? oldData.locationId;

      let entity: any = '';
      if (level === 0) {
        entity = 'Corporate'
      } else if (level === 1) {
        entity = (await this.locationOneRepository.findById(locationId)).name || 'NA'
      } else if (level === 2) {
        entity = (await this.locationTwoRepository.findById(locationId)).name || 'NA'
      } else if (level === 3) {
        entity = (await this.locationThreeRepository.findById(locationId)).name || 'NA'
      }



      // Combine added reporters and consolidators into one array with roles
      const addedUsers: {user: any, role: 'Reporter' | 'Consolidator'}[] = [];

      for (const user of addedReporterProfiles) {
        addedUsers.push({user, role: 'Reporter'});
      }


      // Create subject and body per user
      for (const {user, role} of addedUsers) {
        const body = `
  <p><strong>Dear ${user?.information?.empname || 'User'},</strong></p>
  <p style="margin: 5px 0px;">You have been assigned as the <strong>Data Reporter</strong> for the qualitative section
    <strong>"${section}"</strong> under the topic <strong>"${topic}"</strong> for the reporting entity
<strong>"${entity}"</strong> on the Navigos ESG platform.
  </p>

   <p style="margin: 5px 0px;"><strong>Please find the assignment details below:</strong></p>
    <ul>
      <li><strong>Assigned Consolidator(s):</strong> ${consolidatorData.map((x: any) => x.email).filter((x: any) => x)}</li>
       <li><strong>Submission Due Date:</strong> ${DateTime.fromISO(assignQlEntityUser.due_date || oldData.due_date || '', {zone: 'utc'}).plus({day: 1}).toFormat('dd-LLL-yyyy')}</li>

    </ul>

 ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
      To access the form and begin your response, please log in to the platform using the following link:
      <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a>
    </p>` : ''}

    <p style="margin: 5px 0px;">
      If you have any questions or require assistance, you may raise a ticket through the platform or
      contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
    </p>

    <p style="margin: 5px 0px;">Thank you for your cooperation.</p>


`;

        const subject = `Assignment of Sustainability Qualitative Response Form – Navigos`;

        try {
          const info = await this.sqsService.sendEmail(user.email, subject, body, []).then((info) => {
            console.log('mail sent')

          }).catch((err) => {
            console.log('error in sending')

          })
        } catch (error) {
          console.error(`Error sending email to ${role}:`, error);
          throw new Error('Failed to send email');
        }

        console.log(subject);
        console.log(body);
      }

    }


  }

  @put('/assign-ql-entity-users/{id}')
  @response(204, {
    description: 'AssignQlEntityUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignQlEntityUser: AssignQlEntityUser,
  ): Promise<void> {
    await this.assignQlEntityUserRepository.replaceById(id, assignQlEntityUser);
  }

  @del('/assign-ql-entity-users/{id}')
  @response(204, {
    description: 'AssignQlEntityUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignQlEntityUserRepository.deleteById(id);
  }

  @post('/assigned-ql-entity-users')
  @response(200, {
    description: 'AssignQlEntityUser  success',
  })
  async getAssignmentById(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            properties: {
              userId: {type: 'number'},
              userProfileId: {type: 'number'}
            }
          }
        },
      },
    })
    queryData: {userId: number, userProfileId: number},
  ): Promise<any> {
    const {userProfileId, userId} = queryData

    const entityAssignment = await this.userProfileRepository.assignQlEntities(userProfileId).find()
    if (entityAssignment.length) {
      const entityUserAssignment = (await this.userProfileRepository.assignQlEntityUsers(userProfileId).find({include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]})).filter(x => x.reporter_ids?.includes(userId))
      const locations = await this.userProfileRepository.locationOnes(userProfileId).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

      const locations0 = [0]
      const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
      const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locationMap: any = {
        0: locations0,
        1: locations1,
        2: locations2,
        3: locations3
      };
      const filteredAssignments = entityUserAssignment.filter((assignment) => {
        return entityAssignment.some((ent: any) => {
          const tierKey = `tier${assignment.level}_ids`;
          const validLocations = locationMap[assignment?.level || 0] || [];

          // Filter out invalid IDs from ent[tierKey]
          const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
            validLocations.includes(Number(id))
          );



          // Check if assignment.locationId is present in the filtered IDs
          const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

          const isBasicMatch =
            ent.qCategoryId === assignment.qCategoryId &&
            ent.qTopicId === assignment.qTopicId &&
            ent.qSectionId === assignment.qSectionId;
          return isBasicMatch && isLocationMatch;
        });
      });


      return filteredAssignments.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf);

    } else {
      return []
    }


  }
  @post('/assigned-ql-entity-consolidators')
  @response(200, {
    description: 'AssignQlEntityUser  success',
  })
  async getConsolidatorAssignmentById(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            properties: {
              userId: {type: 'number'},
              userProfileId: {type: 'number'}
            }
          }
        },
      },
    })
    queryData: {userId: number, userProfileId: number},
  ): Promise<any> {
    const {userProfileId, userId} = queryData

    const entityAssignment = await this.userProfileRepository.assignQlEntities(userProfileId).find({include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]})
    const filteredEntityAssignment = entityAssignment.filter(x => userId === 0 || (x.consolidator_ids && x.consolidator_ids?.includes(userId)))
    if (filteredEntityAssignment.length) {
      const entityUserAssignment = (await this.userProfileRepository.assignQlEntityUsers(userProfileId).find({include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]})).filter(x => !userId || x.reporter_ids?.includes(userId))
      const locations = await this.userProfileRepository.locationOnes(userProfileId).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

      const locations0 = [0]
      const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
      const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locationMap: any = {
        0: locations0,
        1: locations1,
        2: locations2,
        3: locations3
      };
      const filteredAssignments = entityUserAssignment.filter((assignment) => {
        return filteredEntityAssignment.some((ent: any) => {
          const tierKey = `tier${assignment.level}_ids`;
          const validLocations = locationMap[assignment?.level || 0] || [];

          // Filter out invalid IDs from ent[tierKey]
          const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
            validLocations.includes(Number(id))
          );



          // Check if assignment.locationId is present in the filtered IDs
          const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

          const isBasicMatch =
            ent.qCategoryId === assignment.qCategoryId &&
            ent.qTopicId === assignment.qTopicId &&
            ent.qSectionId === assignment.qSectionId;
          return isBasicMatch && isLocationMatch;
        });
      });


      return {status: filteredEntityAssignment.length !== 0, consolidation: filteredEntityAssignment.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf), reporter: filteredAssignments.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf)};

    } else {
      return {status: false}
    }


  }
  @get('send-qualitative-reporter-reminder')
  async getQualitativeReporterReminder(): Promise<any> {

    const reporterAssignment = await this.getConsolidatorAssignmentById({userId: 0, userProfileId: 289})

    const filteredAssignments = reporterAssignment?.reporter?.filter((x: any) => x.status !== "Completed") || []

    const reportData: any = []
    const reporter_ids = filteredAssignments.flatMap((d: any) => d?.reporter_ids || []);
    const userPortal = "https://tvsmotor.eisqr.com/"
    const userList = await this.userProfileController.filteredUP({where: {id: {inq: [...reporter_ids]}}})
    const locations = await this.userProfileRepository.locationOnes(289).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})


    for (const assignment of filteredAssignments) {

      if (!assignment.entityAssId) {
        console.warn(`Missing entityAssId in assignment:`, assignment);
        continue;
      }

      const entityAssignment = await this.assignQlEntityRepository.findById(assignment.entityAssId)

      const consolidatorData = await this.userProfileController.filteredUP({where: {id: {inq: entityAssignment?.consolidator_ids || []}}})




      const [reporters, entity] = await Promise.all([
        this.userProfileController.getUsersByIds(assignment.reporter_ids, userList),
        this.userProfileController.getSortedEntity(assignment.level, assignment.locationId, locations)
      ]);
      const reporterNames = reporters.map((user: any) => ({
        id: user.id,
        name: user.information['empname'], email: user.email
      }));

      const consolidateNames = consolidatorData.map((user: any) => (

        user?.information?.['empname'] || ''
      )).filter((x: any) => x).join(',');


      interface Entity {
        name: string;
        // other props if needed
      }

      const formattedDueDate = assignment.due_date
        ? new Date(assignment.due_date).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        : '';

      reportData.push({
        reporterNames,
        "Topic": assignment.qTopic?.name || '',
        "Section": assignment.qSection?.name || '',
        "ReportingEntity": (entity as Entity)?.name || '',
        "DueDate": formattedDueDate || '',
        "consolidatorName": consolidateNames || ''
      });

    }

    const userMailMap: any = {};

    reportData.forEach(({Topic, Section, ReportingEntity, DueDate, consolidatorName, reporterNames = []}: any) => {
      const keyInfo = {Topic, Section, ReportingEntity, DueDate, consolidatorName};

      reporterNames.forEach((user: any) => {
        if (!userMailMap[user.email]) {
          userMailMap[user.email] = {reporter: [], name: user.name};
        }
        userMailMap[user.email].reporter.push(keyInfo);
      });



    });

    return [{
      id: 289,
      email: Object.entries(userMailMap).map(([email, value]) => {
        const user = value as {name: string, reporter: any}; // Explicit type assertion

        return {
          email,
          name: user.name,
          subject: 'Reminder to submit the Assigned Qualitative Response Forms ',
          body: `<div>
                  <p>Dear ${user.name}</p>
                  <p>This is <strong>Reminder[1/2]</strong> to complete your assigned <strong>Qualitative Response Form(s)</strong> as per the submission timeline.</p>
<p>The following submissions are pending from your end and are essential for the completeness of our sustainability data reporting.Timely submission will ensure they are processed and reflected in the performance dashboards and reports.</p>
${this.helper.generateHtmlTable(user.reporter)}
<p>Please log in to the ${userPortal}  to complete and submit the required data along with all supporting documents before the deadline.</p>
<p>If you have already submitted the response form, please disregard this message. </p>

<p>For assistance, contact  <a href="mailto:<EMAIL>" > <EMAIL></a>. If you face any issues, such as incorrect assignments or technical difficulties, please report them immediately.</p>
<p>Thank you for your prompt attention to this matter.
                  <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
                </div>`,
        };
      })
    }];
    ;

  }
}


