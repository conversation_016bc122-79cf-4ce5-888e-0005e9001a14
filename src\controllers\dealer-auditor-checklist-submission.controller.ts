import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
} from '@loopback/rest';
import {DealerAuditorChecklistSubmission} from '../models';
import {DealerAuditorChecklistSubmissionRepository} from '../repositories';

export class DealerAuditorChecklistSubmissionController {
  constructor(
    @repository(DealerAuditorChecklistSubmissionRepository)
    public dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
  ) { }

  @post('/dealer-auditor-checklist-submissions', {
    responses: {
      '200': {
        description: 'DealerAuditorChecklistSubmission model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAuditorChecklistSubmission)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {
            title: 'NewDealerAuditorChecklistSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    dealerAuditorChecklistSubmission: Omit<DealerAuditorChecklistSubmission, 'id'>,
  ): Promise<DealerAuditorChecklistSubmission> {
    return this.dealerAuditorChecklistSubmissionRepository.create(dealerAuditorChecklistSubmission);
  }

  @get('/dealer-auditor-checklist-submissions/count', {
    responses: {
      '200': {
        description: 'DealerAuditorChecklistSubmission model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(DealerAuditorChecklistSubmission) where?: Where<DealerAuditorChecklistSubmission>,
  ): Promise<Count> {
    return this.dealerAuditorChecklistSubmissionRepository.count(where);
  }

  @get('/dealer-auditor-checklist-submissions', {
    responses: {
      '200': {
        description: 'DealerAuditorChecklistSubmission model count',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DealerAuditorChecklistSubmission, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(DealerAuditorChecklistSubmission) filter?: Filter<DealerAuditorChecklistSubmission>,

  ): Promise<DealerAuditorChecklistSubmission[]> {
    return this.dealerAuditorChecklistSubmissionRepository.find(filter);
  }

  @get('/dealer-auditor-checklist-submissions-response', {
    responses: {
      '200': {
        description: 'Array of DealerAuditorChecklistSubmission model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DealerAuditorChecklistSubmission, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async findResponse(
    @param.filter(DealerAuditorChecklistSubmission) filter?: Filter<DealerAuditorChecklistSubmission>,
  ): Promise<any> {
    const getMsiRating = (score: number): string => {
      if (score >= 85) return "Platinum";
      if (score >= 71 && score <= 84) return "Gold";
      if (score >= 56 && score <= 70) return "Silver";
      if (score >= 41 && score <= 55) return "Bronze";
      return "Needs Improvement";
    };
    const zonalOfficeList = {
      1: "Central",
      2: "East",
      3: "North",
      9: "South",
      4: "South1",
      5: "South2",
      8: "West",
      6: "West1",
      7: "West2",
      10: "TN",
      11: "North1",
      12: "North2"
    };

    const dealerType = {
      1: "Authorized Main Dealer",
      2: "Authorized Dealer",
      3: "Authorized Parts Stockist (APS)",
      4: "Area Office"
    };

    const dealerData = await this.dealerAuditorChecklistSubmissionRepository.find({
      include: [{relation: 'vendor'}],
      // fields: {response: false}
    });

    const transformedData = dealerData.map(item => {

      const dealer = item.vendor || {};
      const scores = item.score ? JSON.parse(item.score) : {overallScore: null, salesScore: null, serviceScore: null};
      const parsed = item.response ? JSON.parse(item.response) : [];
      const parsedResponse = Array.isArray(parsed) && Array.isArray(parsed[0]) ? parsed[0] : parsed;

      (parsedResponse as any[]).forEach((entry: any) => {
        if (entry.questions && Array.isArray(entry.questions)) {
          (entry.questions as any[]).forEach((question: any) => {
            if (question.score === undefined || question.score === null) {
              const selectedOption = question.options?.find((opt: any) => opt.checked === 1);
              question.score = selectedOption ? selectedOption.score : 0;
            }
          });

          entry.score = entry.questions.reduce((sum: number, q: any) => sum + (q.score || 0), 0);
        }
      });

      // Initialize empty objects for detailed subCriteria scores
      const salesCriteria: Record<string, number> = {};
      const serviceCriteria: Record<string, number> = {};

      // Initialize empty objects for summed criteria scores
      const salesCriteriaSummary: Record<string, number> = {};
      const serviceCriteriaSummary: Record<string, number> = {};
      // Iterate over parsed response and categorize
      parsedResponse.forEach((entry: any) => {
        if (entry.subCriteria && entry.criteria && entry.score !== undefined) {
          if (entry.section === 1) {
            salesCriteria[entry.subCriteria] = entry.score;
            salesCriteriaSummary[entry.criteria] = (salesCriteriaSummary[entry.criteria] || 0) + entry.score;
          } else if (entry.section === 2) {
            serviceCriteria[entry.subCriteria] = entry.score;
            serviceCriteriaSummary[entry.criteria] = (serviceCriteriaSummary[entry.criteria] || 0) + entry.score;
          }
        }
      });

      // Create separate objects for Sales and Service
      const salesObject = {
        date_of_calibration: item.created_on ? item.created_on.split('T')[0] : null,
        zone: dealer.dealerZone ? zonalOfficeList[dealer.dealerZone as keyof typeof zonalOfficeList] : 'Unknown',
        area_office: dealer.dealerAO || null,
        calibrators: item.created_by || null,
        city: dealer.dealerLocation || null,
        branch_code: dealer.code || null,
        dealer_name: dealer.dealerName || null,
        category: "Sales",  // Hardcoded to indicate Sales category
        score: scores.overallScore,
        total_score: scores.overallScore,
        sales_score: scores.salesScore,
        service_score: scores.serviceScore,
        grade: getMsiRating(item.score ? parseInt(item.score as string) : 0),
        dealerType: dealer.dealerCategory ?? 0,  // Assuming dealerType is a propert
        // Flatten sales criteria
        ...salesCriteria,

        // Flatten sales criteria summary
        ...salesCriteriaSummary
      };

      const serviceObject = {
        date_of_calibration: item.created_on ? item.created_on.split('T')[0] : null,
        zone: dealer.dealerZone ? zonalOfficeList[dealer.dealerZone as keyof typeof zonalOfficeList] : 'Unknown',
        area_office: dealer.dealerAO || null,
        calibrators: item.created_by || null,
        city: dealer.dealerLocation || null,
        branch_code: dealer.code || null,
        dealer_name: dealer.dealerName || null,
        category: "Service",  // Hardcoded to indicate Service category
        score: scores.overallScore,
        total_score: scores.overallScore,
        sales_score: scores.salesScore,
        service_score: scores.serviceScore,
        grade: getMsiRating(item.score ? parseInt(item.score as string) : 0),
        dealerType: dealer.dealerCategory ?? 0,  // Assuming dealerType is a propert

        // Flatten service criteria
        ...serviceCriteria,

        // Flatten service criteria summary
        ...serviceCriteriaSummary
      };

      // Return both objects as an array
      return [salesObject, serviceObject];



    });

    return transformedData.flat();
  }

  @patch('/dealer-auditor-checklist-submissions', {
    responses: {
      '200': {
        description: 'DealerAuditorChecklistSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {partial: true}),
        },
      },
    })
    dealerAuditorChecklistSubmission: DealerAuditorChecklistSubmission,
    @param.where(DealerAuditorChecklistSubmission) where?: Where<DealerAuditorChecklistSubmission>,
  ): Promise<Count> {
    return this.dealerAuditorChecklistSubmissionRepository.updateAll(dealerAuditorChecklistSubmission, where);
  }

  @get('/dealer-auditor-checklist-submissions/{id}', {
    responses: {
      '200': {
        description: 'DealerAuditorChecklistSubmission model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DealerAuditorChecklistSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerAuditorChecklistSubmission>
  ): Promise<DealerAuditorChecklistSubmission> {
    return this.dealerAuditorChecklistSubmissionRepository.findById(id, filter);
  }

  @get('/dealer-auditor-checklist-submissions-response/{id}', {
    responses: {
      '200': {
        description: 'DealerAuditorChecklistSubmission model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findByIdResponse(
    @param.path.number('id') id: number,
    @param.filter(DealerAuditorChecklistSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerAuditorChecklistSubmission>
  ): Promise<any> {

    const getMsiRating = (score: number): string => {
      if (score >= 85) return "Platinum";
      if (score >= 71 && score <= 84) return "Gold";
      if (score >= 56 && score <= 70) return "Silver";
      if (score >= 41 && score <= 55) return "Bronze";
      return "Needs Improvement";
    };

    const zonalOfficeList = {
      1: "Central",
      2: "East",
      3: "North",
      9: "South",
      4: "South1",
      5: "South2",
      8: "West",
      6: "West1",
      7: "West2"
    };

    const dealerType = {
      1: "Authorized Main Dealer",
      2: "Authorized Dealer",
      3: "Authorized Parts Stockist (APS)",
      4: "Area Office"
    };

    const data = await this.dealerAuditorChecklistSubmissionRepository.findById(id, {
      include: [{relation: 'vendor'}]
    });


    // Initialize empty objects for detailed subCriteria scores

    const dealer = data.vendor || {};
    const scores = data.score ? JSON.parse(data.score) : {overallScore: null, salesScore: null, serviceScore: null};
    let parsedResponse = data.response ? JSON.parse(data.response) : [];

    if (Array.isArray(parsedResponse) && parsedResponse.length === 1 && Array.isArray(parsedResponse[0])) {
      parsedResponse = parsedResponse[0];

    }



    (parsedResponse as any[]).forEach((entry: any) => {
      if (entry.questions && Array.isArray(entry.questions)) {
        (entry.questions as any[]).forEach((question: any) => {
          if (question.score === undefined || question.score === null) {
            const selectedOption = question.options?.find((opt: any) => opt.checked === 1);
            question.score = selectedOption ? selectedOption.score : 0;
          }
        });

        entry.score = entry.questions.reduce((sum: number, q: any) => sum + (q.score || 0), 0);
      }
    });
    // Initialize empty objects for storing structured data
    const salesCriteria: Record<string, {summary_score: number; subCriteria: {name: string; score: number}[]}> = {};
    const serviceCriteria: Record<string, {summary_score: number; subCriteria: {name: string; score: number}[]}> = {};

    // Iterate over parsed response and structure the data
    parsedResponse.forEach((entry: any) => {
      if (entry.subCriteria && entry.criteria && entry.score !== undefined) {
        if (entry.section === 1) {
          // Ensure criteria exists in salesCriteria
          if (!salesCriteria[entry.criteria]) {
            salesCriteria[entry.criteria] = {summary_score: 0, subCriteria: []};
          }

          // Add the subCriteria score
          salesCriteria[entry.criteria].subCriteria.push({
            name: entry.subCriteria,
            score: entry.score
          });

          // Update the summary score
          salesCriteria[entry.criteria].summary_score += entry.score;
        } else if (entry.section === 2) {
          // Ensure criteria exists in serviceCriteria
          if (!serviceCriteria[entry.criteria]) {
            serviceCriteria[entry.criteria] = {summary_score: 0, subCriteria: []};
          }

          // Add the subCriteria score
          serviceCriteria[entry.criteria].subCriteria.push({
            name: entry.subCriteria,
            score: entry.score
          });

          // Update the summary score
          serviceCriteria[entry.criteria].summary_score += entry.score;
        }
      }
    });

    return {
      date_of_calibration: data.created_on ? data.created_on.split('T')[0] : null,
      zone: dealer.dealerZone ? zonalOfficeList[dealer.dealerZone as keyof typeof zonalOfficeList] : 'Unknown',
      area_office: dealer.dealerAO || null,
      calibrators: data.created_by || null,
      city: dealer.dealerLocation || null,
      branch_code: dealer.code || null,
      dealer_name: dealer.dealerName || null,
      category: dealer.dealerCategory ? dealerType[dealer.dealerCategory as keyof typeof dealerType] : 'Unknown',

      score: scores.overallScore,
      total_score: scores.overallScore,
      sales_score: scores.salesScore,
      service_score: scores.serviceScore,
      grade: getMsiRating(data.score ? parseInt(data.score as string) : 0),

      // Updated structured format
      sales_criteria: salesCriteria,
      service_criteria: serviceCriteria
    };

  }

  @patch('/dealer-auditor-checklist-submissions/{id}', {
    responses: {
      '204': {
        description: 'DealerAuditorChecklistSubmission PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {partial: true}),
        },
      },
    })
    dealerAuditorChecklistSubmission: DealerAuditorChecklistSubmission,
  ): Promise<void> {
    await this.dealerAuditorChecklistSubmissionRepository.updateById(id, dealerAuditorChecklistSubmission);
  }

  @put('/dealer-auditor-checklist-submissions/{id}', {
    responses: {
      '204': {
        description: 'DealerAuditorChecklistSubmission PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dealerAuditorChecklistSubmission: DealerAuditorChecklistSubmission,
  ): Promise<void> {
    await this.dealerAuditorChecklistSubmissionRepository.replaceById(id, dealerAuditorChecklistSubmission);
  }

  @del('/dealer-auditor-checklist-submissions/{id}', {
    responses: {
      '204': {
        description: 'DealerAuditorChecklistSubmission DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dealerAuditorChecklistSubmissionRepository.deleteById(id);
  }
}
