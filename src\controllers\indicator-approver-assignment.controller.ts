import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {filterAssignmentsByFiscalYear, filterDataByTierAndLocationByLevel, filterObjectsByDateRange, generateApprovedPeriods, getDueMonth, getOverdueDays, getPeriodsForAssignment, getPeriodsForAssignment_filtered, getRPTextFormat} from '../helper/filter-assignment-year-helper';
import {IndicatorApproverAssignment, NewMetric} from '../models';
import {FormCollectionRepository, IndicatorApproverAssignmentRepository, LocationOneRepository, NewCategoryRepository, NewMetricRepository, UserProfileRepository} from '../repositories';
import {Helper} from '../services';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class IndicatorApproverAssignmentController {
  constructor(
    @repository(IndicatorApproverAssignmentRepository)
    public indicatorApproverAssignmentRepository: IndicatorApproverAssignmentRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(NewCategoryRepository)
    public newCategoryRepository: NewCategoryRepository,
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @inject('services.HelperProvider')
    public helper: Helper,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController
  ) { }

  @post('/indicator-approver-assignments')
  @response(200, {
    description: 'IndicatorApproverAssignment model instance',
    content: {'application/json': {schema: getModelSchemaRef(IndicatorApproverAssignment)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorApproverAssignment, {
            title: 'NewIndicatorApproverAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    indicatorApproverAssignment: Omit<IndicatorApproverAssignment, 'id'>,
  ): Promise<IndicatorApproverAssignment> {
    return this.indicatorApproverAssignmentRepository.create(indicatorApproverAssignment);
  }

  @get('/indicator-approver-assignments/count')
  @response(200, {
    description: 'IndicatorApproverAssignment model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IndicatorApproverAssignment) where?: Where<IndicatorApproverAssignment>,
  ): Promise<Count> {
    return this.indicatorApproverAssignmentRepository.count(where);
  }

  @get('/indicator-approver-assignments')
  @response(200, {
    description: 'Array of IndicatorApproverAssignment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IndicatorApproverAssignment, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IndicatorApproverAssignment) filter?: Filter<IndicatorApproverAssignment>,
  ): Promise<IndicatorApproverAssignment[]> {
    return this.indicatorApproverAssignmentRepository.find(filter);
  }

  @patch('/indicator-approver-assignments')
  @response(200, {
    description: 'IndicatorApproverAssignment PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorApproverAssignment, {partial: true}),
        },
      },
    })
    indicatorApproverAssignment: IndicatorApproverAssignment,
    @param.where(IndicatorApproverAssignment) where?: Where<IndicatorApproverAssignment>,
  ): Promise<Count> {
    return this.indicatorApproverAssignmentRepository.updateAll(indicatorApproverAssignment, where);
  }

  @get('/indicator-approver-assignments/{id}')
  @response(200, {
    description: 'IndicatorApproverAssignment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IndicatorApproverAssignment, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(IndicatorApproverAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<IndicatorApproverAssignment>
  ): Promise<IndicatorApproverAssignment> {
    return this.indicatorApproverAssignmentRepository.findById(id, filter);
  }

  @patch('/indicator-approver-assignments/{id}')
  @response(204, {
    description: 'IndicatorApproverAssignment PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorApproverAssignment, {partial: true}),
        },
      },
    })
    indicatorApproverAssignment: IndicatorApproverAssignment,
  ): Promise<void> {
    await this.indicatorApproverAssignmentRepository.updateById(id, indicatorApproverAssignment);
  }

  @put('/indicator-approver-assignments/{id}')
  @response(204, {
    description: 'IndicatorApproverAssignment PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() indicatorApproverAssignment: IndicatorApproverAssignment,
  ): Promise<void> {
    await this.indicatorApproverAssignmentRepository.replaceById(id, indicatorApproverAssignment);
  }

  @del('/indicator-approver-assignments/{id}')
  @response(204, {
    description: 'IndicatorApproverAssignment DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.indicatorApproverAssignmentRepository.deleteById(id);
  }

  @post('/set-location-assignment-for-indicator')
  @response(200, {
    description: 'List of users under the specified locations with indicators',
  })
  async getIndividualUsers(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              indicatorId: {type: 'number'},
              userId: {type: 'number'},

              userProfileId: {type: 'number'},
              levelOfApproval: {type: 'number'},
              locations: {
                type: 'array'

              },
            },
            required: ['userProfileId', 'locations', 'indicatorId'],
          },
        },
      },
    })
    requestBody: {
      userId: number,
      indicatorId: number;
      userProfileId: number;
      levelOfApproval: number,
      locations: number[];
    },
  ): Promise<any> {
    const {userId, indicatorId, locations, userProfileId, levelOfApproval} = requestBody;

    // Find the current indicator assignments
    const assignments_by_level = await this.indicatorApproverAssignmentRepository.findOne({
      where: {
        and: [
          {indicatorId: indicatorId},
          {levelOfApproval: levelOfApproval},
          {userProfileId: userProfileId}
        ],
      },
    });
    const overallAssignments = await this.indicatorApproverAssignmentRepository.find({
      where: {
        and: [
          {indicatorId: indicatorId},
          {userProfileId: userProfileId}
        ],
      },
    });

    // Filter for existing assignment
    const existingAssignment = overallAssignments.find(
      assignment =>
        assignment.indicatorId === indicatorId &&
        (assignment.levelOfApproval === levelOfApproval + 1 || assignment.levelOfApproval === levelOfApproval + 2) &&
        assignment.userProfileId === userProfileId
    );

    // If an assignment already exists, update it
    if (assignments_by_level) {
      assignments_by_level.locations = locations;
      assignments_by_level.modified_on = DateTime.utc().toString()
      assignments_by_level.modified_by = userId
      await this.indicatorApproverAssignmentRepository.update(assignments_by_level);
      return {status: true, assignment: existingAssignment}
    } else {
      // Create a new assignment if it doesn't exist
      const output = await this.indicatorApproverAssignmentRepository.create({
        indicatorId: indicatorId,
        userProfileId: userProfileId,
        locations, created_by: userId, created_on: DateTime.utc().toString()
      });
      return {
        status: true, assignment: output
      }
    }


  }
  @post('/get-possible-selection')
  @response(200, {
    description: 'List of locations filtered by level of approval',
  })
  async getIndicatorApprovalLevel(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              indicatorId: {type: 'number'},
              levelOfApproval: {type: 'number'}, // 1: Country, 2: Region, 3: Location
              userProfileId: {type: 'number'},
              id: {type: 'number', nullable: true}, // null or number
            },
            required: ['indicatorId', 'levelOfApproval', 'userProfileId'],
          },
        },
      }
    })
    requestBody: {
      indicatorId: number;
      levelOfApproval: number;
      userProfileId: number;
      id: number | null; // Nullable id
    },
  ): Promise<any[]> {
    const {indicatorId, levelOfApproval, userProfileId, id} = requestBody;
    let requiredLevel0Ids: number[] = [], requiredLevel1Ids: number[] = [], requiredLevel2Ids: number[] = [], requiredLevel3Ids: number[] = []
    // Fetch all assignments for the given indicatorId and userProfileId
    const assignments = await this.indicatorApproverAssignmentRepository.find({
      where: {indicatorId, userProfileId},
    });
    const clientAssignment = await this.userProfileRepository.assignDcfClients(userProfileId).find()
    if (clientAssignment.length) {
      let metric_ids = clientAssignment[0].metric_ids || []
      const indicatorList = await this.newMetricRepository.find({include: ['newDataPoints']})
      const filteredList = indicatorList.filter((i) => i && i.id && metric_ids.includes(i.id))

      if (filteredList.map(i => i.id).includes(indicatorId)) {
        let standalone_ids = this.filterDerivedAndStandaloneWithIds([filteredList.find(i => i.id === indicatorId)], indicatorList)
        let uniqueStandaloneIds = Array.from(new Set(standalone_ids.flatMap(i => i.standalone_ids)))
        let requiredDcf = Array.from(new Set(
          uniqueStandaloneIds.flatMap(i => {
            // First, find the matching item in indicatorList
            const indicator = indicatorList.find(x => x.id === i);

            // If indicator is found and has newDataPoints, proceed
            if (indicator && indicator.newDataPoints) {
              // Filter newDataPoints and flatten the results
              return indicator.newDataPoints
                .filter(y => Array.isArray(y.data1) && y.data1.length && y.data1[0].datasource)
                .flatMap(z => z && Array.isArray(z.data1) && z.data1[0].datasource);
            }

            // Return null or empty array if no match or newDataPoints not present
            return null;
          }).filter(i => i) // Filter out null values
        ));


        if (requiredDcf.length) {
          const entityAssignment = await this.userProfileRepository.assignDcfEntities(userProfileId).find({where: {dcfId: {inq: requiredDcf}}});

          if (entityAssignment.length) {
            for (const item of entityAssignment) {
              if (item.tier0_ids && item.tier0_ids.length) {
                requiredLevel0Ids = Array.from(new Set([...requiredLevel0Ids, ...item.tier0_ids]))


              }
              if (item.tier1_ids && item.tier1_ids.length) {
                requiredLevel1Ids = Array.from(new Set([...requiredLevel1Ids, ...item.tier1_ids]))


              }
              if (item.tier2_ids && item.tier2_ids.length) {
                requiredLevel2Ids = Array.from(new Set([...requiredLevel2Ids, ...item.tier2_ids]))


              }
              if (item.tier3_ids && item.tier3_ids.length) {
                requiredLevel3Ids = Array.from(new Set([...requiredLevel3Ids, ...item.tier3_ids]))


              }
            }
          }
        }

      }

    }

    // Initialize disabled and checked arrays
    let disabled0: any[] = [], disabled: any[] = [], disabled2: any[] = [], disabled3: any[] = []
    let checkedLocations: any[] = [];

    // Set disabled locations based on existing assignments
    assignments.forEach((assignment) => {
      if (assignment.levelOfApproval != null && assignment.locations) {
        if (assignment.levelOfApproval === 0) {
          disabled0.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 1) {
          disabled.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 2) {
          disabled2.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 3) {
          disabled3.push(...assignment.locations.map(i => i.id));
        }
        // Add assigned locations to disabled
        if (assignment.id !== id && assignment.levelOfApproval === levelOfApproval) {
          checkedLocations.push(...assignment.locations.map(i => i.id)); // Add assigned locations for the passed `id`
        }
      }
    });

    // Fetch locations based on levelOfApproval
    const filteredLocations = await this.userProfileRepository.locationOnes(userProfileId).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    const shapedSite = filteredLocations.map(item => {
      if (item.locationTwos) {
        item.locationTwos = item.locationTwos.filter(locationTwo =>
          locationTwo.locationThrees && locationTwo.locationThrees.length > 0
        );
      }
      return item;
    }).filter(item => item.locationTwos && item.locationTwos.length > 0);

    let data = null
    const result = [{
      name: 'Corporate',
      id: 0, tier0_id: 0, tier1_id: 0, tier2_id: null, tier3_id: undefined,
      disabled: checkedLocations.includes(0),
      selected: disabled0.includes(0),
      checked: (checkedLocations.includes(0) ? 1 : disabled0.includes(0) ? 0 : 2)
    }, ...shapedSite.flatMap((locationOne: any) => {
      // Handle level 1 (Country)
      if (levelOfApproval === 1 && locationOne.id) {
        const isParentDisabled = disabled0.includes(0);
        return [{
          name: locationOne.name,
          id: locationOne.id, tier1_id: locationOne.id, tier2_id: 0, tier3_id: undefined,
          disabled: checkedLocations.includes(locationOne.id),
          selected: isParentDisabled || disabled.includes(locationOne.id),
          checked: (checkedLocations.includes(locationOne.id) ? 1 : !isParentDisabled ? 0 : 2)
        }];
      }

      // Handle level 2 (Region)
      if (levelOfApproval === 2) {

        const isParentDisabled = disabled.includes(locationOne.id) || disabled0.includes(0);
        return locationOne.locationTwos.map((locationTwo: any) => {

          return (
            {
              name: locationTwo.name,
              id: locationTwo.id, tier1_id: locationOne.id, tier2_id: locationTwo.id, tier3_id: 0,
              disabled: checkedLocations.includes(locationTwo.id),
              selected: (isParentDisabled || (disabled2.length && disabled2.includes(locationTwo.id))),
              checked: (checkedLocations.includes(locationTwo.id) ? 1 : !isParentDisabled ? 0 : 2)

            })
        }
        );
      }

      // Handle level 3 (Location)
      if (levelOfApproval === 3) {
        return locationOne.locationTwos.flatMap((locationTwo: any) => {
          const isParentDisabled = disabled.includes(locationOne.id) || disabled2.includes(locationTwo.id) || disabled0.includes(0)
          return locationTwo.locationThrees.map((locationThree: any) => ({
            name: locationThree.name,
            id: locationThree.id, tier1_id: locationOne.id, tier2_id: locationTwo.id, tier3_id: locationThree.id,
            selected: (isParentDisabled || disabled3.includes(locationThree.id)),
            disabled: checkedLocations.includes(locationThree.id),
            checked: (checkedLocations.includes(locationThree.id) ? 1 : !isParentDisabled ? 0 : 2)

          }));
        });
      }

      return [];
    })]

    return result.filter((i) => (levelOfApproval === 0 ? requiredLevel0Ids.includes(0) : levelOfApproval === 1 ? requiredLevel1Ids.includes(i.id) : levelOfApproval === 2 ? requiredLevel2Ids.includes(i.id) : requiredLevel3Ids.includes(i.id)))
  }
  @post('/indicator-approver-assignments-custom')
  @response(200, {
    description: 'List of locations filtered by level of approval',
  })
  async createCustom(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              indicatorId: {type: 'number'},
              userId: {type: 'number'},
              levelOfApproval: {type: 'number'}, // 1: Country, 2: Region, 3: Location
              userProfileId: {type: 'number'},
              id: {type: 'number', nullable: true}, // null or number
              threshold: {type: 'boolean'}
            },
            required: ['indicatorId', 'levelOfApproval', 'userProfileId', 'userId'],
          },
        },
      },
    })
    requestBody: {
      tvalue1: any,
      tvalue2: any,
      responsibility: number[],
      userId: number
      locations: any[],
      indicatorId: number;
      levelOfApproval: number;
      userProfileId: number;
      threshold: boolean,
      id: number | null; // Nullable id
    },
  ): Promise<any> {
    const {indicatorId, levelOfApproval, userId, userProfileId, id, responsibility, locations, threshold, tvalue1, tvalue2} = requestBody;
    let newData: any = null
    let forms = await this.formCollectionRepository.find({
      where: {
        or: [
          {tags: null},
          {tags: []},
          {tags: {inq: [userProfileId]}}
        ]
      }
    })
    // If id is passed, update the specific assignment
    if (id) {
      await this.indicatorApproverAssignmentRepository.updateById(id, {modified_by: userId, modified_on: DateTime.utc().toString(), tvalue1, tvalue2, threshold, locations, responsibility});
    } else {
      newData = await this.indicatorApproverAssignmentRepository.create({locations, created_by: userId, indicatorId, created_on: DateTime.utc().toString(), userProfileId, tvalue1, tvalue2, threshold, levelOfApproval, responsibility});
    }

    // Fetch all assignments for the given indicatorId and userProfileId
    const assignments = await this.indicatorApproverAssignmentRepository.find({
      where: {indicatorId, userProfileId},
    });

    // Initialize disabled and checked arrays
    let disabled0: any[] = [], disabled: any[] = [], disabled2: any[] = [], disabled3: any[] = [], deleteIds: any[] = []
    let checkedLocations: any[] = [];

    // Set disabled locations based on existing assignments
    assignments.forEach((assignment) => {

      if (assignment.levelOfApproval != null && assignment.locations) {
        if (assignment.levelOfApproval === 1) {
          disabled0.push(0);
        } else if (assignment.levelOfApproval === 1) {
          disabled.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 2) {
          disabled2.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 3) {
          disabled3.push(...assignment.locations.map(i => i.id));
        }
        // Add assigned locations to disabled
        if (assignment.id !== id && assignment.levelOfApproval === levelOfApproval) {
          checkedLocations.push(...assignment.locations.map(i => i.id)); // Add assigned locations for the passed `id`
        }
      }
    });



    assignments.forEach(async (item) => {
      if (item.levelOfApproval && item.locations) {
        if (levelOfApproval === 0) {
          if (item.levelOfApproval === 1 || item.levelOfApproval === 2 || item.levelOfApproval === 3) {
            let filteredLevel0 = assignments.filter(i => i.levelOfApproval === 0).flatMap(i => i.locations).map(i => i.id)

            let filteredLevel1 = assignments.filter(i => i.levelOfApproval === 1).flatMap(i => i.locations).map(i => i.id)
            let filteredLevel2 = assignments.filter(i => i.levelOfApproval === 2).flatMap(i => i.locations).map(i => i.id)

            const isParentDisabled0 = item.locations?.some(j => filteredLevel0.includes(0))
            const isParentDisabled = item.locations?.some(j => filteredLevel1.includes(j.tier1_id))

            if (item.levelOfApproval === 1) {
              if (isParentDisabled0) {
                const locations = item.locations?.filter(j => !filteredLevel0.includes(0))
                if (locations?.length) {
                  await this.indicatorApproverAssignmentRepository.updateById(item.id, {locations})
                } else {
                  deleteIds.push(item.id)
                  await this.indicatorApproverAssignmentRepository.deleteById(item.id)
                }
              }
            } else if (item.levelOfApproval === 2) {


              if (isParentDisabled || isParentDisabled0) {
                const locations = item.locations?.filter(j => !filteredLevel0.includes(0) && !filteredLevel1.includes(j.tier1_id))
                if (locations?.length) {
                  await this.indicatorApproverAssignmentRepository.updateById(item.id, {locations})
                } else {
                  deleteIds.push(item.id)
                  await this.indicatorApproverAssignmentRepository.deleteById(item.id)
                }
              }
            } else if (item.levelOfApproval === 3) {
              const isParentDisabled2 = item.locations?.some(j => filteredLevel2.includes(j.tier2_id))


              if (isParentDisabled || isParentDisabled2 || isParentDisabled0) {

                const locations = item.locations?.filter(j => !filteredLevel0.includes(0) && !filteredLevel2.includes(j.tier2_id) && !filteredLevel1.includes(j.tier1_id))

                if (locations?.length) {
                  await this.indicatorApproverAssignmentRepository.updateById(item.id, {locations})
                } else {
                  deleteIds.push(item.id)
                  await this.indicatorApproverAssignmentRepository.deleteById(item.id)
                }

              }

            }



          }
        } else if (levelOfApproval === 1) {
          if (item.levelOfApproval === 2 || item.levelOfApproval === 3) {
            let filteredLevel1 = assignments.filter(i => i.levelOfApproval === 1).flatMap(i => i.locations).map(i => i.id)
            let filteredLevel2 = assignments.filter(i => i.levelOfApproval === 2).flatMap(i => i.locations).map(i => i.id)
            const isParentDisabled = item.locations?.some(j => filteredLevel1.includes(j.tier1_id))

            if (item.levelOfApproval === 2) {
              if (isParentDisabled) {

                const locations = item.locations?.filter(j => !filteredLevel1.includes(j.tier1_id))

                if (locations?.length) {
                  await this.indicatorApproverAssignmentRepository.updateById(item.id, {locations})
                } else {
                  deleteIds.push(item.id)
                  await this.indicatorApproverAssignmentRepository.deleteById(item.id)
                }
              }
            } else if (item.levelOfApproval === 3) {
              const isParentDisabled2 = item.locations?.some(j => filteredLevel2.includes(j.tier2_id))

              if (isParentDisabled || isParentDisabled2) {

                const locations = item.locations?.filter(j => !filteredLevel2.includes(j.tier2_id) && !filteredLevel1.includes(j.tier1_id))

                if (locations?.length) {
                  await this.indicatorApproverAssignmentRepository.updateById(item.id, {locations})
                } else {
                  deleteIds.push(item.id)
                  await this.indicatorApproverAssignmentRepository.deleteById(item.id)
                }
              }

            }



          }
        } else if (levelOfApproval === 2) {
          if (item.levelOfApproval === 3) {
            let filteredLevel1 = assignments.filter(i => i.levelOfApproval === 1).flatMap(i => i.locations).map(i => i.id)
            let filteredLevel2 = assignments.filter(i => i.levelOfApproval === 2).flatMap(i => i.locations).map(i => i.id)
            const isParentDisabled = item.locations?.some(j => filteredLevel1.includes(j.tier1_id))

            if (item.levelOfApproval === 3) {
              const isParentDisabled2 = item.locations?.some(j => filteredLevel2.includes(j.tier2_id))
              if (isParentDisabled || isParentDisabled2) {

                const locations = item.locations?.filter(j => !filteredLevel2.includes(j.tier2_id))

                if (locations?.length) {
                  await this.indicatorApproverAssignmentRepository.updateById(item.id, {locations})
                } else {
                  deleteIds.push(item.id)
                  await this.indicatorApproverAssignmentRepository.deleteById(item.id)
                }
              }

            }



          }
        }
      }
    })

    const updatedAssignment = await this.indicatorApproverAssignmentRepository.findById(id || newData.id)

    let dcfData: any = []
    let assignmentData: any = []
    if (updatedAssignment && updatedAssignment.indicatorId && userProfileId) {
      const adminDetail = (await this.userProfileController.filteredUP({where: {id: userProfileId}}))?.[0]
      // Fetch locations based on levelOfApproval
      const filteredLocations = await this.userProfileRepository.locationOnes(userProfileId).find({
        include: [
          {
            relation: "locationTwos",
            scope: {
              include: [{relation: "locationThrees"}],
            },
          },
        ],
      });

      const shapedSite = filteredLocations.map(item => {
        if (item.locationTwos) {
          item.locationTwos = item.locationTwos.filter(locationTwo =>
            locationTwo.locationThrees && locationTwo.locationThrees.length > 0
          );
        }
        return item;
      }).filter(item => item.locationTwos && item.locationTwos.length > 0);

      const metric = await this.newMetricRepository.find({where: {id: updatedAssignment.indicatorId}, include: ['newDataPoints']})
      const esgCategory = await this.newCategoryRepository.find({
        include: [
          {
            relation: "newTopics",
            scope: {
              include: [{
                relation: "newMetrics", scope: {
                  include: ["newDataPoints"],
                }
              }],
            },
          },
        ],
      });
      const shapedCategory = esgCategory.map(item => {
        if (item.newTopics) {
          item.newTopics = item.newTopics.filter(topics =>
            topics.newMetrics && topics.newMetrics.length > 0
          );
        }
        return item;
      }).filter(item => item.newTopics && item.newTopics.length > 0)
      const res = await this.getPendingStatusOfAllIndicators(userProfileId, updatedAssignment.indicatorId, forms.map(i => i.id))
      const dcfIds = Array.from(new Set(res.uniqueStandaloneIds.flatMap(i => metric.find(x => x.id === updatedAssignment.indicatorId)?.newDataPoints?.filter(y => Array.isArray(y?.data1) && y?.data1?.length && y?.data1[0]?.datasource).flatMap((z: any) => z?.data1[0]?.datasource) || []).filter(i => i)))
      const dcfUserAssignment = await this.userProfileRepository.assignDcfEntityUsers(userProfileId).find({where: {dcfId: {inq: dcfIds}}, include: ['dcf']})
      console.log(res)
      for (const entity of res.entities) {
        let filteredUserAssignment = dcfUserAssignment.filter((x) => x.locationId === entity.locationId && x.level === entity.level)
        let filteredAssignmentCurrent = filterDataByTierAndLocationByLevel(filteredUserAssignment, shapedSite, entity.tier1_id, entity.tier2_id, entity.tier3_id)
        console.log(filteredUserAssignment.length)

        dcfData.push(...filteredAssignmentCurrent)
      }
      const userIds = [...(updatedAssignment?.responsibility || []),
      ...new Set(
        dcfData.flatMap((item: any) => [
          ...(item.reporter_ids ?? []),
          ...(item.reviewer_ids ?? [])
        ])
      )
      ];
      const numericUserIds = userIds.filter((x): x is number => typeof x === 'number');
      const userList = await this.userProfileController.filteredUP({where: {id: {inq: numericUserIds}}})
      const assignedDCFByIndicator: any = []
      for (const assignment of dcfData) {
        const [reporters, reviewers, entity] = await Promise.all([
          this.userProfileController.getUsersByIds(assignment?.reporter_ids || [], userList),
          this.userProfileController.getUsersByIds(assignment?.reviewer_ids || [], userList),
          this.userProfileController.getSortedEntity(assignment.level, assignment.locationId, shapedSite)
        ]);
        const reporterNames = reporters.map((user: any) => ({
          id: user.id,
          name: user.information['empname'], email: user.email
        }));
        const reviewerNames = reviewers.map((user: any) => ({
          id: user.id,
          name: user.information['empname'], email: user.email
        }));
        type Entity = {
          name: string;
          id: number
        };
        const frequency_list = [{name: 'Monthly', id: 1}, {name: 'Bi-Monthly', id: 2}, {name: 'Quarterly', id: 3}, {name: 'Annually', id: 4}, {name: 'Bi-Annually', id: 5}, {name: 'Undefined', id: 6}]
        const entityName: Entity = entity as Entity;
        console.log(entityName)
        assignedDCFByIndicator.push({"Data Collection Form": assignment?.dcf?.title, "Reporting Entity": entityName?.name || '', "Reporting Period": assignment.start_date ? DateTime.fromISO(assignment.start_date, {zone: 'utc'}).plus({'day': 1}).toFormat('LLL-yyyy') : 'NA', "Frequency": frequency_list.find(x => x.id === assignment.frequency)?.name || 'NA', "Reporter(s)": reporterNames.map(x => x.name).filter(x => x), "Reviewer(s)": reviewerNames.map(x => x.name).filter(x => x).length ? reviewerNames.map(x => x.name).filter(x => x) : 'Self Reviewer'})
      }
      for (const userId of (updatedAssignment?.responsibility || [])) {
        let find = userList.find((x: any) => x.id === userId)
        if (find?.email) {
          const body = `<div>  <p>Dear ${find?.information?.empname || 'Approver'}</p>
      <p>You have been assigned as the <strong>Data Approver</strong> for the raw data related to the indicator <strong>"${metric.find(x => x.id === updatedAssignment.indicatorId)?.title}"</strong>.  The assignment details are provided below</p>
<p><strong>Submission Due Date:</strong> 30th of the month succeeding the reporting period </p>
      ${this.helper.generateHtmlTable(assignedDCFByIndicator)}
      <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform</a> to log in and complete the data approval by the due date. </p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>
        </div>`
          assignmentData.push(body)
          const info = await this.sqsService.sendEmail(find.email, "Assignment of Sustainability Data Collection Form for Data Approval - Navigos", body, [])

        }
      }



    }


    return {status: true, new: newData ? true : false, data: newData, deleteIds, body: assignmentData}
  }


  filterDerivedAndStandaloneWithIds(data: any[], overall: any[]) {
    const childIds = new Set();
    const standaloneChildren: any = {};

    // Function to collect standalone IDs recursively for both children and nested parents
    function collectStandaloneIds(itemId: number) {
      const item = overall.find(d => d.id === itemId);
      if (!item || !item.data1[0]) return [];

      let standaloneIds = item.data1[0].indicator.filter((id: any) => {
        const child = overall.find(d => d.id === id);
        return child && child.data1[0]?.source === 1; // Check if the child is standalone
      });

      // Recursively collect standalone children from nested parents
      item.data1[0].indicator.forEach((id: any) => {
        const child = overall.find(d => d.id === id);
        if (child && child.data1[0]?.source === 0) {
          const nestedStandaloneIds = collectStandaloneIds(child.id);
          standaloneIds = standaloneIds.concat(nestedStandaloneIds); // Merge nested results
        }
      });

      return standaloneIds;
    }

    // Collect standalone children for derived parents
    data.forEach(item => {
      if (item.data1[0]?.type === 0 && item.data1[0]?.source === 0) {
        const standaloneIds = collectStandaloneIds(item.id);
        if (standaloneIds.length > 0) {
          standaloneChildren[item.id] = standaloneIds;
        }

        // Add all child IDs (standalone or not) to the set of child IDs
        item.data1[0].indicator.forEach((id: unknown) => childIds.add(id));
      }
    });

    // Filter out derived children and attach standalone_ids to derived parents
    const filteredData = data.map(item => {
      // If item is a derived parent and has standalone children, add standalone_ids
      if (standaloneChildren[item.id]) {
        return {...item, standalone_ids: standaloneChildren[item.id]};
      }

      // Check for standalone items
      if (item.data1[0]?.type === 0 && item.data1[0]?.source === 1) {
        return {...item, standalone_ids: [item.id]};
      }

      // Retain only if it's not a derived child (id not in childIds)
      if (!childIds.has(item.id)) {
        return item;
      }

      return null; // Exclude derived children
    }).filter(item => item !== null); // Remove null values

    return filteredData;
  }

  async getPendingStatusOfAllIndicators(userProfileId: number, indicatorId: number, filteredDCF: any) {

    let requiredLevel0Ids: number[] = [], requiredLevel1Ids: number[] = [], requiredLevel2Ids: number[] = [], requiredLevel3Ids: number[] = [], uniqueStandaloneIds: number[] = []
    // Fetch all assignments for the given indicatorId and userProfileId
    const assignments = await this.indicatorApproverAssignmentRepository.find({
      where: {indicatorId, userProfileId},
    });
    const clientAssignment = await this.userProfileRepository.assignDcfClients(userProfileId).find()
    if (clientAssignment.length) {
      let metric_ids = clientAssignment[0].metric_ids || []
      const indicatorList = await this.newMetricRepository.find({include: ['newDataPoints']})
      const filteredList = indicatorList.filter((i) => i && i.id && metric_ids.includes(i.id))

      if (filteredList.map(i => i.id).includes(indicatorId)) {
        let standalone_ids = this.filterDerivedAndStandaloneWithIds([filteredList.find(i => i.id === indicatorId)], indicatorList)
        uniqueStandaloneIds = Array.from(new Set(standalone_ids.flatMap(i => i.standalone_ids)))
        let requiredDcf = Array.from(new Set(
          uniqueStandaloneIds.flatMap(i => {
            // First, find the matching item in indicatorList
            const indicator = indicatorList.find(x => x.id === i);

            // If indicator is found and has newDataPoints, proceed
            if (indicator && indicator.newDataPoints) {
              // Filter newDataPoints and flatten the results
              return indicator.newDataPoints
                .filter(y => Array.isArray(y.data1) && y.data1.length && y.data1[0].datasource)
                .flatMap(z => z && Array.isArray(z.data1) && z.data1[0].datasource).filter(x => filteredDCF.includes(x))
            }

            // Return null or empty array if no match or newDataPoints not present
            return null;
          }).filter(i => i) // Filter out null values
        ));


        if (requiredDcf.length) {
          const entityAssignment = await this.userProfileRepository.assignDcfEntities(userProfileId).find({where: {dcfId: {inq: requiredDcf}}});

          if (entityAssignment.length) {
            for (const item of entityAssignment) {
              if (item.tier0_ids && item.tier0_ids.length) {

                requiredLevel0Ids = Array.from(new Set([...requiredLevel0Ids, ...item.tier0_ids]))
              }
              if (item.tier1_ids && item.tier1_ids.length) {
                requiredLevel1Ids = Array.from(new Set([...requiredLevel1Ids, ...item.tier1_ids]))


              }
              if (item.tier2_ids && item.tier2_ids.length) {
                requiredLevel2Ids = Array.from(new Set([...requiredLevel2Ids, ...item.tier2_ids]))


              }
              if (item.tier3_ids && item.tier3_ids.length) {
                requiredLevel3Ids = Array.from(new Set([...requiredLevel3Ids, ...item.tier3_ids]))


              }
            }
          }
        }

      }

    }

    // Initialize disabled and checked arrays
    let disabled: any[] = [], disabled2: any[] = [], disabled3: any[] = [], disabled0: any[] = []
    let checkedLocations: any[] = [], checkedLocations2: any[] = [], checkedLocations3: any[] = [], checkedLocations0: any[] = [];

    // Set disabled locations based on existing assignments
    assignments.forEach((assignment) => {

      if (assignment.levelOfApproval != null && assignment.locations) {

        if (assignment.levelOfApproval === 0) {

          disabled0.push(0);
        } else if (assignment.levelOfApproval === 1) {
          disabled.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 2) {
          disabled2.push(...assignment.locations.map(i => i.id));
        } else if (assignment.levelOfApproval === 3) {
          disabled3.push(...assignment.locations.map(i => i.id));
        }
        // Add assigned locations to disabled
        if (assignment.levelOfApproval === 0) {
          checkedLocations0.push(0); // Add assigned locations for the passed `id`
        } else if (assignment.levelOfApproval === 1) {
          checkedLocations.push(...assignment.locations.map(i => i.id)); // Add assigned locations for the passed `id`
        } else if (assignment.levelOfApproval === 2) {
          checkedLocations2.push(...assignment.locations.map(i => i.id)); // Add assigned locations for the passed `id`
        } else if (assignment.levelOfApproval === 3) {
          checkedLocations3.push(...assignment.locations.map(i => i.id)); // Add assigned locations for the passed `id`
        }
      }
    });

    // Fetch locations based on levelOfApproval
    const filteredLocations = await this.userProfileRepository.locationOnes(userProfileId).find({
      include: [
        {
          relation: "locationTwos",
          scope: {
            include: [{relation: "locationThrees"}],
          },
        },
      ],
    });

    const shapedSite = filteredLocations.map(item => {
      if (item.locationTwos) {
        item.locationTwos = item.locationTwos.filter(locationTwo =>
          locationTwo.locationThrees && locationTwo.locationThrees.length > 0
        );
      }
      return item;
    }).filter(item => item.locationTwos && item.locationTwos.length > 0);

    let data = null
    const result = [{
      name: "Corporate",
      level: 0,
      locationId: 0,
      id: 0,
      tier1_id: 0,
      tier2_id: null,
      tier3_id: undefined,
      disabled: checkedLocations0.includes(0),
      selected: disabled0.includes(0),
      checked: checkedLocations0.includes(0) ? 1 : disabled0.includes(0) ? 0 : 2,
    }, ...shapedSite.flatMap((locationOne) => [

      {
        level: 1,
        locationId: locationOne.id,
        name: locationOne.name,
        id: locationOne.id,
        tier1_id: locationOne.id,
        tier2_id: 0,
        tier3_id: undefined,
        parent: disabled0.includes(0) ? 'Corporate' : disabled.includes(locationOne.id) ? locationOne.name : null,
        disabled: checkedLocations.includes(locationOne.id),
        selected: disabled0.includes(0) || disabled.includes(locationOne.id),
        checked: checkedLocations.includes(locationOne.id) ? 1 : disabled0.includes(0) || disabled.includes(locationOne.id) ? 0 : 2,
      },
      // Level 2 (Region)
      ...locationOne.locationTwos.map((locationTwo) => ({
        level: 2,
        locationId: locationTwo.id,
        name: locationTwo.name,
        id: locationTwo.id,
        tier1_id: locationOne.id,
        tier2_id: locationTwo.id,
        tier3_id: 0,
        parent: disabled0.includes(0) ? 'Corporate' : disabled.includes(locationOne.id) ? locationOne.name : null,
        disabled: checkedLocations2.includes(locationTwo.id),
        selected: disabled0.includes(0) || disabled2.includes(locationTwo.id) || disabled.includes(locationOne.id),
        checked: checkedLocations2.includes(locationTwo.id)
          ? 1
          : disabled0.includes(0) || disabled2.includes(locationTwo.id) || disabled.includes(locationOne.id)
            ? 0
            : 2,
      })),
      // Level 3 (Location)
      ...locationOne.locationTwos.flatMap((locationTwo) =>
        locationTwo.locationThrees.map((locationThree) => ({
          level: 3,
          locationId: locationThree.id,
          name: locationThree.name,
          id: locationThree.id,
          tier1_id: locationOne.id,
          tier2_id: locationTwo.id,
          tier3_id: locationThree.id,
          parent: disabled0.includes(0) ? 'Corporate' : disabled.includes(locationOne.id) ? locationOne.name : disabled2.includes(locationTwo.id) ? locationTwo.name : null,
          disabled: checkedLocations3.includes(locationThree.id),
          selected: disabled0.includes(0) || disabled3.includes(locationThree.id) || disabled.includes(locationOne.id) || disabled2.includes(locationTwo.id),
          checked: checkedLocations3.includes(locationThree.id)
            ? 1
            : disabled0.includes(0) || disabled3.includes(locationThree.id) || disabled.includes(locationOne.id) || disabled2.includes(locationTwo.id)
              ? 0
              : 2,
        }))
      ),
    ])]

    return {
      uniqueStandaloneIds, entities: result.filter((i) => {
        if (i.id === null || i.id === undefined) return false;
        let data: any[][]
        // Filter by the required IDs for each level

        if (i.tier1_id === 0) {
          return requiredLevel0Ids.includes(0)
        } else if (i.id && i.tier1_id && i.tier2_id === 0 && (i.tier3_id == null || i.tier3_id == undefined)) {
          return requiredLevel1Ids.includes(i.id)
        } else if (i.id && i.tier1_id && i.tier2_id && i.tier3_id === 0) {
          return requiredLevel2Ids.includes(i.id); // Level 2 (Region)
        } else if (i.id && i.tier3_id) {
          return requiredLevel3Ids.includes(i.id); // Level 1 (Country)
        }

        return false;
      })
    }


  }

  @post('/get-incomplete-indicators-list')
  @response(200, {
    description: 'List of locations filtered by level of approval',
  })
  async getIncompleteIndicatorsList(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {

              userProfileId: {type: 'number'}
            },
            required: ['userProfileId'],
          },
        },
      },
    })
    requestBody: {

      userProfileId: number;

    },
  ): Promise<any> {
    const {userProfileId} = requestBody;
    let clientAssignments = await this.userProfileRepository.assignDcfClients(userProfileId).find()
    let forms = await this.formCollectionRepository.find({
      where: {
        or: [
          {tags: null},
          {tags: []},
          {tags: {inq: [userProfileId]}}
        ]
      }
    })

    if (clientAssignments.length === 1) {
      let assignedData = clientAssignments[clientAssignments.length - 1]
      let indicator_list: NewMetric[] = []
      if (assignedData && assignedData.metric_ids && assignedData.topic_ids) {
        const esgCategory = await this.newCategoryRepository.find({
          include: [
            {
              relation: "newTopics",
              scope: {
                include: [{
                  relation: "newMetrics", scope: {
                    include: ["newDataPoints"],
                  }
                }],
              },
            },
          ],
        });
        const shapedCategory = esgCategory.map(item => {
          if (item.newTopics) {
            item.newTopics = item.newTopics.filter(topics =>
              topics.newMetrics && topics.newMetrics.length > 0
            );
          }
          return item;
        }).filter(item => item.newTopics && item.newTopics.length > 0)
        shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
          if (assignedData.topic_ids && top.id && assignedData.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === userProfileId)) {
            top.newMetrics.forEach((met) => {

              if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0 && met.data1[0].source === 1) && met.id && assignedData.metric_ids && assignedData.metric_ids.includes(met.id) && !indicator_list.map(i => i.id).includes(met.id) && (met.tag === null || parseFloat(met.tag) === userProfileId)) {
                indicator_list.push(met)

              }
            })
          }
        })
        for (const item of indicator_list) {
          if (item.id) {

            const res = await this.getPendingStatusOfAllIndicators(userProfileId, item.id, forms.map(i => i.id))

            item['entities'] = res.entities
            item['standalone_ids'] = res.uniqueStandaloneIds

          }
        }
        return {status: indicator_list.length !== 0, data: indicator_list.map(x => {const {newDataPoints, ...rest} = x; return rest}), message: 'Indicator Assignment Found'}
      } else {
        return {status: false, data: [], message: 'Indicator Assignment Missing'}
      }
    } else {
      return {status: false, data: [], message: 'Indicator Assignment Missing'}
    }


  }

  @post('/get-my-approve-indicators')
  @response(200, {
    description: 'List of indicator assigned for approval',
  })
  async getMyApproverIndicators(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {type: 'number'},
              userProfileId: {type: 'number'},
              year: {type: 'number'},
              fymonth: {type: 'number'}
            },
            required: ['userProfileId', 'userId', 'year', 'fymonth'],
          },
        },
      },
    })
    requestBody: {

      userProfileId: number; userId: number, year: number, fymonth: number

    },
  ): Promise<any> {
    const {userProfileId, userId, year, fymonth} = requestBody;
    let clientAssignments = await this.userProfileRepository.assignDcfClients(userProfileId).find()
    let indicatorAssignment = await this.userProfileRepository.indicatorApproverAssignments(userProfileId).find()
    let forms = await this.formCollectionRepository.find({
      where: {
        or: [
          {tags: null},
          {tags: []},
          {tags: {inq: [userProfileId]}}
        ]
      }
    })
    const assignedAssignment = indicatorAssignment.filter(x => (x.responsibility?.includes(userId) || userId === 0))
    const list: any = []
    if (clientAssignments.length === 1 && assignedAssignment.length) {
      const dcfAssignment = await this.userProfileRepository.assignDcfEntityUsers(userProfileId).find()
      const locations = await this.userProfileRepository.locationOnes(userProfileId).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

      let assignedIndicator = assignedAssignment.map(x => x.indicatorId).filter(x => x)
      let assignedData = clientAssignments[clientAssignments.length - 1]
      let indicator_list: NewMetric[] = []
      if (assignedData && assignedData.metric_ids && assignedData.topic_ids) {
        const esgCategory = await this.newCategoryRepository.find({
          include: [
            {
              relation: "newTopics",
              scope: {
                include: [{
                  relation: "newMetrics", scope: {
                    include: ["newDataPoints"],
                  }
                }],
              },
            },
          ],
        });
        const shapedCategory = esgCategory.map(item => {
          if (item.newTopics) {
            item.newTopics = item.newTopics.filter(topics =>
              topics.newMetrics && topics.newMetrics.length > 0
            );
          }
          return item;
        }).filter(item => item.newTopics && item.newTopics.length > 0)
        shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
          if (assignedData.topic_ids && top.id && assignedData.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === userProfileId)) {
            top.newMetrics.forEach((met) => {

              if (assignedIndicator.includes(met.id) && (Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0 && met.data1[0].source === 1) && met.id && assignedData.metric_ids && assignedData.metric_ids.includes(met.id) && !indicator_list.map(i => i.id).includes(met.id) && (met.tag === null || parseFloat(met.tag) === userProfileId)) {
                indicator_list.push(met)

              }
            })
          }
        })
        for (const item of indicator_list) {
          if (item.id) {

            const res = await this.getPendingStatusOfAllIndicators(userProfileId, item.id, forms.map(i => i.id))
            const dcfIds = Array.from(new Set(res.uniqueStandaloneIds.flatMap(i => item?.newDataPoints?.filter(y => Array.isArray(y?.data1) && y?.data1?.length && y?.data1[0]?.datasource).flatMap((z: any) => z?.data1[0]?.datasource) || []).filter(i => i)))
            item['entities'] = res.entities
            item['standalone_ids'] = res.uniqueStandaloneIds
            item['dcfIds'] = dcfIds
            const submissions = await this.userProfileRepository.quantitativeSubmissions(userProfileId).find({where: {dcfId: {inq: dcfIds}}, fields: {type: true, id: true, dcfId: true, reject: true, edit: true, reporting_period: true, entityAssId: true, entityUserAssId: true, reporter_modified_by: true, reviewer_modified_by: true, locationId: true, level: true}})
            const filteredDcfassignment = dcfAssignment.filter(x => dcfIds?.includes(x.dcfId))

            for (const entity of res.entities) {
              let filteredUserAssignment = filteredDcfassignment.filter((x) => x.locationId === entity.locationId && x.level === entity.level).map(i => ({...i, periods: getPeriodsForAssignment(i.start_date, i.end_date, i.frequency === 4 ? 12 : i.frequency === 5 ? 6 : i.frequency, fymonth, year)}))
              const approvingMonths = generateApprovedPeriods(filteredUserAssignment.map(i => i.periods))
              let filteredAssignmentCurrent = filterDataByTierAndLocationByLevel(!year ? filteredUserAssignment : filterAssignmentsByFiscalYear(filteredUserAssignment, year, fymonth), locations, entity.tier1_id, entity.tier2_id, entity.tier3_id)
              for (const requiredrp of approvingMonths) {

                let requiredAssignment = filterObjectsByDateRange(requiredrp, filteredAssignmentCurrent)

                const requiredAssignmentsWithMissingPeriods = requiredAssignment.flatMap(i => {
                  // Get all periods for the assignment
                  const periods = getPeriodsForAssignment_filtered(i.start_date, i.end_date, i.frequency === 4 ? 12 : i.frequency === 5 ? 6 : i.frequency, fymonth, year, requiredrp);

                  // Filter out already submitted periods and conditionally add refobj when found
                  return periods
                    .map(period => {
                      // Find the corresponding submission for this specific period
                      const foundSubmission = submissions.find(sub =>
                        i.entityAssId === sub.entityAssId && sub.entityUserAssId === i.id &&
                        i.dcfId === sub.dcfId &&
                        getRPTextFormat(sub.reporting_period) === period
                      );
                      console.log(foundSubmission, 'found',)
                      let status = null
                      if (foundSubmission) {

                        let type = foundSubmission.type
                        let reject = foundSubmission.reject
                        status = (type === 0 && !reject) ? 0 :
                          (type === 0 && (reject === 1 || reject === 2))
                            ? 1
                            : type === 1 && reject === 1
                              ? 2
                              : type === 1
                                ? 3
                                : type === 2
                                  ? 4
                                  : type === 3
                                    ? 5
                                    : null
                        if (status === 0 || status === 1) {

                          status = getOverdueDays(period) <= -DateTime.utc().toLocal().daysInMonth ? 7 : getOverdueDays(period) <= 0 ? 6 : 99


                        }
                      } else {
                        status = getOverdueDays(period) <= -DateTime.utc().toLocal().daysInMonth ? 7 : getOverdueDays(period) <= 0 ? 6 : 100

                      }

                      return {

                        ...i, dueMonth: getDueMonth(period), reporting_period: period,
                        reporters: i.reporter_ids,
                        reviewers: i.reviewer_ids,
                        period, status,
                        ...(foundSubmission ? {data: foundSubmission} : {})
                      };
                    })

                });
                const status = getOverdueDays(requiredrp) >= 0
                  ? 100
                  : getOverdueDays(requiredrp) >= -10
                    ? 6 : getOverdueDays(requiredrp) >= -15 ? 9
                      : 7
                const statusCode = (requiredAssignmentsWithMissingPeriods.length !== 0 && (requiredAssignmentsWithMissingPeriods.every(x => x.status === 5) ? 5 : requiredAssignmentsWithMissingPeriods.some(x => x.status === 4) ? 6 : null))
                list.push({currentStatus: (requiredAssignmentsWithMissingPeriods.length !== 0 && (requiredAssignmentsWithMissingPeriods.every(x => x.status === 5) ? "Approved" : requiredAssignmentsWithMissingPeriods.every(x => x.status === 4) ? "Pending Approval" : "NA")), timeLine: status === 6 ? 'Due Now' : status === 7 ? "Overdue" : status === 9 ? "Duesoon" : "Upcoming", statusCode, datasources: requiredAssignmentsWithMissingPeriods, entity: entity.name, reporting_period: requiredrp, title: (statusCode ? statusCode === 5 ? "View " : statusCode === 6 ? "Approve " : " " : " ") + " Raw Data submission for calculation of " + (item?.data1?.[0]?.title || item.title) + " of " + entity.name + " for " + requiredrp})

              }
            }

          }
        }
        return {list, status: true, data: indicator_list.map(x => {const {newDataPoints, ...rest} = x; return rest}), message: 'Indicator Assignment Found'}
      } else {
        return {status: false, data: [], message: 'Indicator Assignment Missing'}
      }
    } else {
      return {status: false, data: [], message: 'Indicator Assignment Missing'}
    }


  }

  @get('/get-indicator-approver-assignments/{id}')
  @response(200, {
    description: 'IndicatorApproverAssignment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IndicatorApproverAssignment, {includeRelations: true}),
      },
    },
  })
  async findIndicatorByUPId(
    @param.path.number('id') upid: number,
    @param.filter(IndicatorApproverAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<IndicatorApproverAssignment>
  ): Promise<any> {
    return []
  }


  async getUser(id: any) {
    const userList = await this.userProfileController.filteredUP({where: {id}})

    if (userList?.length === 1) {
      return userList[0]?.role === 'clientadmin' ? 'Enterprise Admin' : userList[0]?.role === 'clientuser' ? userList[0]?.information?.empname || 'User Not Found' : "N/A"

    } else {
      return "NA"
    }

  }



}
