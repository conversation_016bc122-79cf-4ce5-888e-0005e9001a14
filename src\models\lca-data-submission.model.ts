import {belongsTo, Entity, model, property} from '@loopback/repository';
import {VendorCode} from './vendor-code.model';

@model()
export class LcaDataSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  supplierId?: number;

  @property({
    type: 'string',
  })
  vendorCode?: string;

  @property({
    type: 'string',
  })
  partNumber?: string;

  @property({
    type: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierInformation?: object | null;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'any',
  })
  imdsId?: any;

  @property({
    type: 'string',
  })
  lastUpdateDate?: string;

  @property({
    type: 'any',
  })
  dataCollectionStatus?: any;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'boolean',
    jsonSchema: {
      nullable: true,
    }
  })
  lock?: boolean | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  reject?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_on?: string | null;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  data?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  completed_stage?: any[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  process?: string[];

  @belongsTo(() => VendorCode)
  vendorId: number;

  constructor(data?: Partial<LcaDataSubmission>) {
    super(data);
  }
}

export interface LcaDataSubmissionRelations {
  // describe navigational properties here
}

export type LcaDataSubmissionWithRelations = LcaDataSubmission & LcaDataSubmissionRelations;
