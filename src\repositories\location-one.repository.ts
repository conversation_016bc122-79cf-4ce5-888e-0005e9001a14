import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {LocationOne, LocationOneRelations, LocationTwo} from '../models';
import {LocationTwoRepository} from './location-two.repository';

export class LocationOneRepository extends DefaultCrudRepository<
  LocationOne,
  typeof LocationOne.prototype.id,
  LocationOneRelations
> {

  public readonly locationTwos: HasManyRepositoryFactory<LocationTwo, typeof LocationOne.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>,
  ) {
    super(LocationOne, dataSource);
    this.locationTwos = this.createHasManyRepositoryFactoryFor('locationTwos', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwos', this.locationTwos.inclusionResolver);
  }
}
